"""
Function registry that manages all available functions in the pipeline.
"""

from typing import Dict, List, Any, Callable
from src.models.schemas import FunctionSchema, FunctionParameter, FunctionParameterType
import json
import logging

logger = logging.getLogger(__name__)


class FunctionRegistry:
    """Registry for managing all available functions."""
    
    def __init__(self):
        self._functions: Dict[str, Callable] = {}
        self._schemas: Dict[str, FunctionSchema] = {}
        self._load_all_functions()
    
    def register_function(self, schema: FunctionSchema, implementation: Callable):
        """Register a function with its schema and implementation."""
        self._functions[schema.name] = implementation
        self._schemas[schema.name] = schema
        logger.info(f"Registered function: {schema.name}")
    
    def get_function(self, name: str) -> Callable:
        """Get function implementation by name."""
        if name not in self._functions:
            raise ValueError(f"Function '{name}' not found")
        return self._functions[name]
    
    def get_schema(self, name: str) -> FunctionSchema:
        """Get function schema by name."""
        if name not in self._schemas:
            raise ValueError(f"Function schema '{name}' not found")
        return self._schemas[name]
    
    def list_functions(self) -> List[FunctionSchema]:
        """Get all function schemas."""
        return list(self._schemas.values())
    
    def get_functions_by_category(self, category: str) -> List[FunctionSchema]:
        """Get functions by category."""
        return [schema for schema in self._schemas.values() if schema.category == category]
    
    def get_categories(self) -> List[str]:
        """Get all available categories."""
        return list(set(schema.category for schema in self._schemas.values()))
    
    def search_functions(self, query: str) -> List[FunctionSchema]:
        """Search functions by name or description."""
        query = query.lower()
        results = []
        for schema in self._schemas.values():
            if (query in schema.name.lower() or 
                query in schema.description.lower() or
                any(query in example.lower() for example in schema.examples)):
                results.append(schema)
        return results
    
    def validate_function_call(self, function_name: str, parameters: Dict[str, Any]) -> bool:
        """Validate a function call against its schema."""
        if function_name not in self._schemas:
            return False
        
        schema = self._schemas[function_name]
        
        # Check required parameters
        for param in schema.parameters:
            if param.required and param.name not in parameters:
                return False
        
        # Check parameter types (basic validation)
        for param_name, param_value in parameters.items():
            param_schema = next((p for p in schema.parameters if p.name == param_name), None)
            if param_schema:
                if not self._validate_parameter_type(param_value, param_schema.type):
                    return False
        
        return True
    
    def _validate_parameter_type(self, value: Any, expected_type: FunctionParameterType) -> bool:
        """Validate parameter type."""
        if expected_type == FunctionParameterType.ANY:
            return True
        elif expected_type == FunctionParameterType.STRING:
            return isinstance(value, str)
        elif expected_type == FunctionParameterType.INTEGER:
            return isinstance(value, int)
        elif expected_type == FunctionParameterType.FLOAT:
            return isinstance(value, (int, float))
        elif expected_type == FunctionParameterType.BOOLEAN:
            return isinstance(value, bool)
        elif expected_type == FunctionParameterType.LIST:
            return isinstance(value, list)
        elif expected_type == FunctionParameterType.DICT:
            return isinstance(value, dict)
        return False
    
    def _load_all_functions(self):
        """Load all function definitions."""
        # Import all function modules
        from . import data_operations
        from . import communication
        from . import file_operations
        from . import web_operations
        from . import analytics
        from . import database
        from . import scheduling
        from . import financial
        from . import system_operations
        
        # Register functions from each module
        data_operations.register_functions(self)
        communication.register_functions(self)
        file_operations.register_functions(self)
        web_operations.register_functions(self)
        analytics.register_functions(self)
        database.register_functions(self)
        scheduling.register_functions(self)
        financial.register_functions(self)
        system_operations.register_functions(self)
        
        logger.info(f"Loaded {len(self._functions)} functions across {len(self.get_categories())} categories")


# Global function registry instance
function_registry = FunctionRegistry()
