"""
Core pipeline that orchestrates query processing and execution.
"""

from typing import Dict, Any, Optional
from src.models.schemas import (
    QueryRequest, QueryResponse, ExecutionPlan, FunctionCall,
    ExecutionRequest, ExecutionResult
)
from src.pipeline.ai_client import OllamaClient, AIPlanner
from src.pipeline.executor import ExecutionEngine
import logging
import time
from datetime import datetime

logger = logging.getLogger(__name__)


class FunctionCallingPipeline:
    """Main pipeline for processing queries and generating function call sequences."""
    
    def __init__(self, ollama_url: str = "http://localhost:11434", model: str = "qwen2.5:7b"):
        self.ollama_client = OllamaClient(ollama_url, model)
        self.ai_planner = AIPlanner(self.ollama_client)
        self.executor = ExecutionEngine()
        self.start_time = time.time()
    
    def is_ready(self) -> bool:
        """Check if the pipeline is ready to process requests."""
        return self.ollama_client.is_available()
    
    def process_query(self, request: QueryRequest) -> QueryResponse:
        """Process a natural language query and return execution plan."""
        start_time = time.time()
        
        try:
            logger.info(f"Processing query: {request.query}")
            
            # Check if AI service is available
            if not self.is_ready():
                return QueryResponse(
                    success=False,
                    error="AI service (Ollama) is not available. Please ensure Ollama is running and the model is loaded.",
                    processing_time=time.time() - start_time
                )
            
            # Get available functions
            available_functions = self.executor.get_available_functions()
            
            # Generate execution plan using AI
            planning_result = self.ai_planner.plan_execution(request.query, available_functions)
            
            if not planning_result["success"]:
                return QueryResponse(
                    success=False,
                    error=f"Failed to generate execution plan: {planning_result['error']}",
                    processing_time=time.time() - start_time
                )
            
            # Parse the plan
            plan_data = planning_result["plan"]
            
            # Convert to ExecutionPlan object
            function_calls = []
            for call_data in plan_data.get("function_calls", []):
                function_call = FunctionCall(
                    function_name=call_data.get("function_name", ""),
                    parameters=call_data.get("parameters", {}),
                    output_variable=call_data.get("output_variable"),
                    depends_on=call_data.get("depends_on", [])
                )
                function_calls.append(function_call)
            
            execution_plan = ExecutionPlan(
                query=request.query,
                reasoning=plan_data.get("reasoning", "AI-generated execution plan"),
                function_calls=function_calls
            )
            
            # Validate the execution plan
            validation = self.executor.validate_execution_plan(execution_plan)
            if not validation["valid"]:
                return QueryResponse(
                    success=False,
                    error=f"Invalid execution plan: {'; '.join(validation['errors'])}",
                    processing_time=time.time() - start_time
                )
            
            processing_time = time.time() - start_time
            
            return QueryResponse(
                success=True,
                execution_plan=execution_plan,
                processing_time=processing_time
            )
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return QueryResponse(
                success=False,
                error=f"Internal error: {str(e)}",
                processing_time=time.time() - start_time
            )
    
    def execute_plan(self, request: ExecutionRequest) -> ExecutionResult:
        """Execute a function call sequence."""
        logger.info(f"Executing plan with {len(request.execution_plan.function_calls)} function calls")
        
        return self.executor.execute_plan(request.execution_plan, request.dry_run)
    
    def process_and_execute(self, query: str, dry_run: bool = False) -> Dict[str, Any]:
        """Process query and optionally execute the plan in one call."""
        # Process query to get execution plan
        query_request = QueryRequest(query=query)
        query_response = self.process_query(query_request)
        
        if not query_response.success:
            return {
                "success": False,
                "error": query_response.error,
                "processing_time": query_response.processing_time
            }
        
        # Execute the plan
        execution_request = ExecutionRequest(
            execution_plan=query_response.execution_plan,
            dry_run=dry_run
        )
        execution_result = self.execute_plan(execution_request)
        
        return {
            "success": execution_result.success,
            "query": query,
            "execution_plan": {
                "reasoning": query_response.execution_plan.reasoning,
                "function_calls": [
                    {
                        "function_name": call.function_name,
                        "parameters": call.parameters,
                        "output_variable": call.output_variable,
                        "depends_on": call.depends_on
                    }
                    for call in query_response.execution_plan.function_calls
                ]
            },
            "execution_results": execution_result.results,
            "errors": execution_result.errors,
            "processing_time": query_response.processing_time,
            "execution_time": execution_result.execution_time,
            "total_time": query_response.processing_time + execution_result.execution_time
        }
    
    def get_function_library(self) -> Dict[str, Any]:
        """Get information about the function library."""
        functions = self.executor.get_available_functions()
        categories = {}
        
        for func in functions:
            category = func["category"]
            if category not in categories:
                categories[category] = []
            categories[category].append(func)
        
        return {
            "total_functions": len(functions),
            "categories": list(categories.keys()),
            "functions_by_category": categories,
            "all_functions": functions
        }
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get pipeline health status."""
        return {
            "status": "healthy" if self.is_ready() else "unhealthy",
            "model_available": self.ollama_client.is_available(),
            "functions_loaded": len(self.executor.get_available_functions()),
            "uptime": time.time() - self.start_time,
            "model": self.ollama_client.model,
            "ollama_url": self.ollama_client.base_url
        }
