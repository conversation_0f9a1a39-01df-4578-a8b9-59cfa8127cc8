import { APIResource } from "../resource.js";
import * as Core from "../core.js";
export declare class Embeddings extends APIResource {
    /**
     * Creates an embedding vector representing the input text.
     *
     * @example
     * ```ts
     * const createEmbeddingResponse =
     *   await client.embeddings.create({
     *     input: 'The quick brown fox jumped over the lazy dog',
     *     model: 'nomic-embed-text-v1_5',
     *   });
     * ```
     */
    create(body: EmbeddingCreateParams, options?: Core.RequestOptions): Core.APIPromise<CreateEmbeddingResponse>;
}
export interface CreateEmbeddingResponse {
    /**
     * The list of embeddings generated by the model.
     */
    data: Array<Embedding>;
    /**
     * The name of the model used to generate the embedding.
     */
    model: string;
    /**
     * The object type, which is always "list".
     */
    object: 'list';
    /**
     * The usage information for the request.
     */
    usage: CreateEmbeddingResponse.Usage;
}
export declare namespace CreateEmbeddingResponse {
    /**
     * The usage information for the request.
     */
    interface Usage {
        /**
         * The number of tokens used by the prompt.
         */
        prompt_tokens: number;
        /**
         * The total number of tokens used by the request.
         */
        total_tokens: number;
    }
}
/**
 * Represents an embedding vector returned by embedding endpoint.
 */
export interface Embedding {
    /**
     * The embedding vector, which is a list of floats. The length of vector depends on
     * the model as listed in the [embedding guide](/docs/guides/embeddings).
     */
    embedding: Array<number> | string;
    /**
     * The index of the embedding in the list of embeddings.
     */
    index: number;
    /**
     * The object type, which is always "embedding".
     */
    object: 'embedding';
}
export interface EmbeddingCreateParams {
    /**
     * Input text to embed, encoded as a string or array of tokens. To embed multiple
     * inputs in a single request, pass an array of strings or array of token arrays.
     * The input must not exceed the max input tokens for the model, cannot be an empty
     * string, and any array must be 2048 dimensions or less.
     */
    input: string | Array<string>;
    /**
     * ID of the model to use.
     */
    model: (string & {}) | 'nomic-embed-text-v1_5';
    /**
     * The format to return the embeddings in. Can only be `float` or `base64`.
     */
    encoding_format?: 'float' | 'base64';
    /**
     * A unique identifier representing your end-user, which can help us monitor and
     * detect abuse.
     */
    user?: string | null;
}
export declare namespace Embeddings {
    export { type CreateEmbeddingResponse as CreateEmbeddingResponse, type Embedding as Embedding, type EmbeddingCreateParams as EmbeddingCreateParams, };
}
//# sourceMappingURL=embeddings.d.ts.map