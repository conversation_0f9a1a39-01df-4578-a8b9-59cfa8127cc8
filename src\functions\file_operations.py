"""
File operations functions for the AI pipeline.
"""

from typing import Any, Dict, List, Optional
from src.models.schemas import FunctionSchema, FunctionParameter, FunctionParameterType
import json
import os
from datetime import datetime


def read_file(file_path: str, encoding: str = "utf-8") -> str:
    """Read content from a file."""
    try:
        with open(file_path, 'r', encoding=encoding) as f:
            return f.read()
    except Exception as e:
        return f"Error reading file: {str(e)}"


def write_file(file_path: str, content: str, encoding: str = "utf-8", append: bool = False) -> Dict[str, Any]:
    """Write content to a file."""
    try:
        mode = 'a' if append else 'w'
        with open(file_path, mode, encoding=encoding) as f:
            f.write(content)
        return {"success": True, "file_path": file_path, "bytes_written": len(content.encode(encoding))}
    except Exception as e:
        return {"success": False, "error": str(e)}


def copy_file(source: str, destination: str) -> Dict[str, Any]:
    """Copy a file from source to destination."""
    # Mock implementation
    return {"success": True, "source": source, "destination": destination, "copied_at": datetime.now().isoformat()}


def move_file(source: str, destination: str) -> Dict[str, Any]:
    """Move a file from source to destination."""
    # Mock implementation
    return {"success": True, "source": source, "destination": destination, "moved_at": datetime.now().isoformat()}


def delete_file(file_path: str) -> Dict[str, Any]:
    """Delete a file."""
    # Mock implementation
    return {"success": True, "file_path": file_path, "deleted_at": datetime.now().isoformat()}


def list_files(directory: str, pattern: str = "*", recursive: bool = False) -> List[str]:
    """List files in a directory."""
    # Mock implementation
    return [f"{directory}/file1.txt", f"{directory}/file2.csv", f"{directory}/file3.json"]


def get_file_info(file_path: str) -> Dict[str, Any]:
    """Get information about a file."""
    # Mock implementation
    return {
        "path": file_path,
        "size": 1024,
        "created": "2024-03-01T10:00:00",
        "modified": "2024-03-15T14:30:00",
        "extension": file_path.split('.')[-1] if '.' in file_path else ""
    }


def compress_files(files: List[str], archive_name: str, format: str = "zip") -> Dict[str, Any]:
    """Compress files into an archive."""
    # Mock implementation
    return {
        "success": True,
        "archive_name": archive_name,
        "format": format,
        "files_count": len(files),
        "created_at": datetime.now().isoformat()
    }


def extract_archive(archive_path: str, destination: str) -> Dict[str, Any]:
    """Extract files from an archive."""
    # Mock implementation
    return {
        "success": True,
        "archive_path": archive_path,
        "destination": destination,
        "extracted_files": 5,
        "extracted_at": datetime.now().isoformat()
    }


def register_functions(registry):
    """Register all file operation functions."""
    
    registry.register_function(
        FunctionSchema(
            name="read_file",
            description="Read content from a file",
            category="file_operations",
            parameters=[
                FunctionParameter(name="file_path", type=FunctionParameterType.STRING, description="Path to the file"),
                FunctionParameter(name="encoding", type=FunctionParameterType.STRING, description="File encoding", required=False, default="utf-8")
            ],
            returns="File content as string",
            examples=["read_file('/path/to/document.txt')"]
        ),
        read_file
    )
    
    registry.register_function(
        FunctionSchema(
            name="write_file",
            description="Write content to a file",
            category="file_operations",
            parameters=[
                FunctionParameter(name="file_path", type=FunctionParameterType.STRING, description="Path to the file"),
                FunctionParameter(name="content", type=FunctionParameterType.STRING, description="Content to write"),
                FunctionParameter(name="encoding", type=FunctionParameterType.STRING, description="File encoding", required=False, default="utf-8"),
                FunctionParameter(name="append", type=FunctionParameterType.BOOLEAN, description="Append to file", required=False, default=False)
            ],
            returns="Write operation result",
            examples=["write_file('/path/to/output.txt', 'Hello World')"]
        ),
        write_file
    )
    
    registry.register_function(
        FunctionSchema(
            name="copy_file",
            description="Copy a file from source to destination",
            category="file_operations",
            parameters=[
                FunctionParameter(name="source", type=FunctionParameterType.STRING, description="Source file path"),
                FunctionParameter(name="destination", type=FunctionParameterType.STRING, description="Destination file path")
            ],
            returns="Copy operation result",
            examples=["copy_file('/source/file.txt', '/backup/file.txt')"]
        ),
        copy_file
    )
    
    registry.register_function(
        FunctionSchema(
            name="move_file",
            description="Move a file from source to destination",
            category="file_operations",
            parameters=[
                FunctionParameter(name="source", type=FunctionParameterType.STRING, description="Source file path"),
                FunctionParameter(name="destination", type=FunctionParameterType.STRING, description="Destination file path")
            ],
            returns="Move operation result",
            examples=["move_file('/temp/file.txt', '/archive/file.txt')"]
        ),
        move_file
    )
    
    registry.register_function(
        FunctionSchema(
            name="delete_file",
            description="Delete a file",
            category="file_operations",
            parameters=[
                FunctionParameter(name="file_path", type=FunctionParameterType.STRING, description="Path to the file to delete")
            ],
            returns="Delete operation result",
            examples=["delete_file('/temp/old_file.txt')"]
        ),
        delete_file
    )
    
    registry.register_function(
        FunctionSchema(
            name="list_files",
            description="List files in a directory",
            category="file_operations",
            parameters=[
                FunctionParameter(name="directory", type=FunctionParameterType.STRING, description="Directory path"),
                FunctionParameter(name="pattern", type=FunctionParameterType.STRING, description="File pattern", required=False, default="*"),
                FunctionParameter(name="recursive", type=FunctionParameterType.BOOLEAN, description="Search recursively", required=False, default=False)
            ],
            returns="List of file paths",
            examples=["list_files('/documents', '*.pdf', True)"]
        ),
        list_files
    )
    
    registry.register_function(
        FunctionSchema(
            name="get_file_info",
            description="Get information about a file",
            category="file_operations",
            parameters=[
                FunctionParameter(name="file_path", type=FunctionParameterType.STRING, description="Path to the file")
            ],
            returns="File information dictionary",
            examples=["get_file_info('/path/to/file.txt')"]
        ),
        get_file_info
    )
    
    registry.register_function(
        FunctionSchema(
            name="compress_files",
            description="Compress files into an archive",
            category="file_operations",
            parameters=[
                FunctionParameter(name="files", type=FunctionParameterType.LIST, description="List of file paths to compress"),
                FunctionParameter(name="archive_name", type=FunctionParameterType.STRING, description="Name of the archive"),
                FunctionParameter(name="format", type=FunctionParameterType.STRING, description="Archive format", required=False, default="zip", enum_values=["zip", "tar", "gz"])
            ],
            returns="Compression result",
            examples=["compress_files(['/file1.txt', '/file2.txt'], 'backup.zip')"]
        ),
        compress_files
    )
    
    registry.register_function(
        FunctionSchema(
            name="extract_archive",
            description="Extract files from an archive",
            category="file_operations",
            parameters=[
                FunctionParameter(name="archive_path", type=FunctionParameterType.STRING, description="Path to the archive"),
                FunctionParameter(name="destination", type=FunctionParameterType.STRING, description="Extraction destination")
            ],
            returns="Extraction result",
            examples=["extract_archive('/backup.zip', '/extracted/')"]
        ),
        extract_archive
    )
