#!/bin/bash
set -e

echo "Starting AI Function Calling Pipeline..."

# Start Ollama in the background
echo "Starting Ollama service..."
ollama serve &
OLLAMA_PID=$!

# Wait for Ollama to be ready
echo "Waiting for <PERSON>lla<PERSON> to start..."
for i in {1..30}; do
    if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
        echo "Ollama is ready!"
        break
    fi
    echo "Waiting for Ollama... ($i/30)"
    sleep 2
done

# Pull the model if not already present
echo "Checking for Qwen 2.5 7B model..."
if ! ollama list | grep -q "qwen2.5:7b"; then
    echo "Downloading Qwen 2.5 7B model... This may take a while."
    ollama pull qwen2.5:7b
else
    echo "Qwen 2.5 7B model already available."
fi

# Start the Python application
echo "Starting AI Function Calling Pipeline server..."
python -m src.main

# Clean up
kill $OLLAMA_PID 2>/dev/null || true
