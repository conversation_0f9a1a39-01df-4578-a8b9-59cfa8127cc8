"""Analytics functions for the AI pipeline."""

from typing import Any, Dict, List, Optional
from src.models.schemas import FunctionSchema, FunctionParameter, FunctionParameterType
from datetime import datetime
import json


def calculate_statistics(data: List[float], metrics: List[str] = None) -> Dict[str, float]:
    """Calculate statistical metrics for numerical data."""
    if not data:
        return {}
    
    stats = {
        "count": len(data),
        "sum": sum(data),
        "mean": sum(data) / len(data),
        "min": min(data),
        "max": max(data)
    }
    
    if len(data) > 1:
        variance = sum((x - stats["mean"]) ** 2 for x in data) / (len(data) - 1)
        stats["variance"] = variance
        stats["std_dev"] = variance ** 0.5
    
    return stats


def create_chart(data: List[Dict[str, Any]], chart_type: str, x_field: str, y_field: str) -> Dict[str, Any]:
    """Create a chart from data."""
    return {
        "success": True,
        "chart_type": chart_type,
        "data_points": len(data),
        "x_field": x_field,
        "y_field": y_field,
        "chart_url": f"https://charts.example.com/{chart_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    }


def generate_report(data: List[Dict[str, Any]], template: str, title: str) -> Dict[str, Any]:
    """Generate a formatted report from data."""
    return {
        "success": True,
        "title": title,
        "template": template,
        "data_records": len(data),
        "report_url": f"https://reports.example.com/{title.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "generated_at": datetime.now().isoformat()
    }


def analyze_trends(data: List[Dict[str, Any]], time_field: str, value_field: str) -> Dict[str, Any]:
    """Analyze trends in time-series data."""
    return {
        "trend": "increasing",
        "slope": 0.15,
        "correlation": 0.85,
        "data_points": len(data),
        "time_range": {"start": "2024-01-01", "end": "2024-03-31"},
        "analysis_date": datetime.now().isoformat()
    }


def create_dashboard(widgets: List[Dict[str, Any]], title: str) -> Dict[str, Any]:
    """Create a dashboard with multiple widgets."""
    return {
        "success": True,
        "dashboard_id": f"dash_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "title": title,
        "widgets_count": len(widgets),
        "dashboard_url": f"https://dashboards.example.com/{title.replace(' ', '_')}",
        "created_at": datetime.now().isoformat()
    }


def export_data(data: List[Dict[str, Any]], format: str, filename: str) -> Dict[str, Any]:
    """Export data to various formats."""
    return {
        "success": True,
        "format": format,
        "filename": filename,
        "records_exported": len(data),
        "file_size": len(json.dumps(data)),
        "exported_at": datetime.now().isoformat()
    }


def register_functions(registry):
    """Register analytics functions."""
    
    registry.register_function(
        FunctionSchema(name="calculate_statistics", description="Calculate statistical metrics", category="analytics",
                      parameters=[FunctionParameter(name="data", type=FunctionParameterType.LIST, description="Numerical data"),
                                FunctionParameter(name="metrics", type=FunctionParameterType.LIST, description="Metrics to calculate", required=False)],
                      returns="Statistical metrics", examples=["calculate_statistics([1, 2, 3, 4, 5])"]),
        calculate_statistics
    )
    
    registry.register_function(
        FunctionSchema(name="create_chart", description="Create a chart from data", category="analytics",
                      parameters=[FunctionParameter(name="data", type=FunctionParameterType.LIST, description="Chart data"),
                                FunctionParameter(name="chart_type", type=FunctionParameterType.STRING, description="Chart type", enum_values=["bar", "line", "pie", "scatter"]),
                                FunctionParameter(name="x_field", type=FunctionParameterType.STRING, description="X-axis field"),
                                FunctionParameter(name="y_field", type=FunctionParameterType.STRING, description="Y-axis field")],
                      returns="Chart creation result", examples=["create_chart(sales_data, 'bar', 'month', 'revenue')"]),
        create_chart
    )
    
    registry.register_function(
        FunctionSchema(name="generate_report", description="Generate formatted report", category="analytics",
                      parameters=[FunctionParameter(name="data", type=FunctionParameterType.LIST, description="Report data"),
                                FunctionParameter(name="template", type=FunctionParameterType.STRING, description="Report template"),
                                FunctionParameter(name="title", type=FunctionParameterType.STRING, description="Report title")],
                      returns="Report generation result", examples=["generate_report(monthly_data, 'summary', 'Monthly Sales Report')"]),
        generate_report
    )
    
    registry.register_function(
        FunctionSchema(name="analyze_trends", description="Analyze trends in time-series data", category="analytics",
                      parameters=[FunctionParameter(name="data", type=FunctionParameterType.LIST, description="Time-series data"),
                                FunctionParameter(name="time_field", type=FunctionParameterType.STRING, description="Time field name"),
                                FunctionParameter(name="value_field", type=FunctionParameterType.STRING, description="Value field name")],
                      returns="Trend analysis results", examples=["analyze_trends(sales_data, 'date', 'amount')"]),
        analyze_trends
    )
    
    registry.register_function(
        FunctionSchema(name="create_dashboard", description="Create dashboard with widgets", category="analytics",
                      parameters=[FunctionParameter(name="widgets", type=FunctionParameterType.LIST, description="Dashboard widgets"),
                                FunctionParameter(name="title", type=FunctionParameterType.STRING, description="Dashboard title")],
                      returns="Dashboard creation result", examples=["create_dashboard([chart1, chart2], 'Sales Dashboard')"]),
        create_dashboard
    )
    
    registry.register_function(
        FunctionSchema(name="export_data", description="Export data to various formats", category="analytics",
                      parameters=[FunctionParameter(name="data", type=FunctionParameterType.LIST, description="Data to export"),
                                FunctionParameter(name="format", type=FunctionParameterType.STRING, description="Export format", enum_values=["csv", "json", "xlsx", "pdf"]),
                                FunctionParameter(name="filename", type=FunctionParameterType.STRING, description="Output filename")],
                      returns="Export result", examples=["export_data(report_data, 'csv', 'monthly_report.csv')"]),
        export_data
    )
