#!/usr/bin/env python3
"""
Test server startup for debugging.
"""

import sys
import os
from pathlib import Path

# Setup paths
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))
os.chdir(project_root)

print("=== AI Function Calling Pipeline Test ===")
print(f"Working directory: {os.getcwd()}")
print(f"Python version: {sys.version}")

try:
    print("\n1. Testing imports...")
    from src.functions.registry import function_registry
    print("   ✓ Function registry imported")
    
    from src.pipeline.core import FunctionCallingPipeline
    print("   ✓ Pipeline core imported")
    
    from src.api.routes import app
    print("   ✓ API routes imported")
    
    print("\n2. Testing function registry...")
    functions = function_registry.list_functions()
    print(f"   ✓ {len(functions)} functions loaded")
    
    print("\n3. Testing pipeline...")
    pipeline = FunctionCallingPipeline()
    health = pipeline.get_health_status()
    print(f"   ✓ Pipeline status: {health['status']}")
    print(f"   ✓ Functions available: {health['functions_loaded']}")
    
    print("\n4. Starting server...")
    import uvicorn
    
    print("   Server starting on http://127.0.0.1:8000")
    print("   Press Ctrl+C to stop")
    
    uvicorn.run(
        app, 
        host="127.0.0.1", 
        port=8000, 
        log_level="debug"
    )
    
except Exception as e:
    print(f"\n❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
