<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Function Calling Pipeline</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🤖 AI Function Calling Pipeline</h1>
            <p>Convert natural language queries into structured function call sequences</p>
            <div class="status-indicator">
                <span id="status-dot" class="status-dot"></span>
                <span id="status-text">Checking status...</span>
            </div>
        </header>

        <main>
            <!-- Query Input Section -->
            <section class="query-section">
                <h2>Enter Your Query</h2>
                <div class="input-group">
                    <textarea 
                        id="query-input" 
                        placeholder="Example: Retrieve all invoices for March, summarize the total amount, and send the summary to my email."
                        rows="3"
                    ></textarea>
                    <div class="button-group">
                        <button id="process-btn" class="btn btn-primary">Generate Plan</button>
                        <button id="execute-btn" class="btn btn-secondary" disabled>Execute Plan</button>
                        <button id="dry-run-btn" class="btn btn-outline">Dry Run</button>
                    </div>
                </div>
            </section>

            <!-- Examples Section -->
            <section class="examples-section">
                <h3>Example Queries</h3>
                <div id="examples-container" class="examples-grid">
                    <!-- Examples will be loaded here -->
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="results-section" style="display: none;">
                <h2>Results</h2>
                
                <!-- Execution Plan -->
                <div class="result-card">
                    <h3>📋 Execution Plan</h3>
                    <div id="reasoning" class="reasoning"></div>
                    <div id="function-calls" class="function-calls"></div>
                </div>

                <!-- Execution Results -->
                <div class="result-card" id="execution-results" style="display: none;">
                    <h3>⚡ Execution Results</h3>
                    <div id="execution-output"></div>
                </div>

                <!-- Timing Information -->
                <div class="timing-info" id="timing-info" style="display: none;">
                    <span id="processing-time"></span>
                    <span id="execution-time"></span>
                    <span id="total-time"></span>
                </div>
            </section>

            <!-- Function Library Section -->
            <section class="library-section">
                <h2>📚 Function Library</h2>
                <div class="library-stats" id="library-stats">
                    <span>Loading function library...</span>
                </div>
                <div class="categories-grid" id="categories-grid">
                    <!-- Function categories will be loaded here -->
                </div>
            </section>
        </main>

        <footer>
            <p>Powered by Qwen 2.5 7B via Ollama | Built with FastAPI & Vanilla JS</p>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
        <p id="loading-text">Processing your query...</p>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>
