# Changelog

## 0.25.0 (2025-06-12)

Full Changelog: [v0.24.0...v0.25.0](https://github.com/groq/groq-typescript/compare/v0.24.0...v0.25.0)

### Features

* **api:** api update ([c7a3183](https://github.com/groq/groq-typescript/commit/c7a31832b59799ce7fb087cb118fa1057ff742d9))
* **api:** api update ([458d3cf](https://github.com/groq/groq-typescript/commit/458d3cfd76c82360358669d70758a66eadee4e74))
* **api:** api update ([d620864](https://github.com/groq/groq-typescript/commit/d62086446bb0f587f3b975690fde35d0708b72c6))

## 0.24.0 (2025-06-11)

Full Changelog: [v0.23.0...v0.24.0](https://github.com/groq/groq-typescript/compare/v0.23.0...v0.24.0)

### Features

* **api:** api update ([79a02be](https://github.com/groq/groq-typescript/commit/79a02beec00f9daf920f8e9a4cf341904ac083c5))


### Chores

* **docs:** use top-level-await in example snippets ([9277f5d](https://github.com/groq/groq-typescript/commit/9277f5da347d88d7236c2a5b2e57fee95727d186))

## 0.23.0 (2025-05-29)

Full Changelog: [v0.22.0...v0.23.0](https://github.com/groq/groq-typescript/compare/v0.22.0...v0.23.0)

### Features

* **api:** api update ([527632f](https://github.com/groq/groq-typescript/commit/527632fca9ab7589cef1b1eb9e74da6645706433))
* **api:** api update ([478f2e8](https://github.com/groq/groq-typescript/commit/478f2e8ab8f0c07fe5ae192d00c843b47f78bd6c))
* **api:** api update ([f25c88a](https://github.com/groq/groq-typescript/commit/f25c88a35d599425abe1e8b53951b4be1066eab5))


### Chores

* **docs:** grammar improvements ([bd25707](https://github.com/groq/groq-typescript/commit/bd25707f2a699527fb7f8ea9f055e3ef040f29c6))
* improve publish-npm script --latest tag logic ([40719b5](https://github.com/groq/groq-typescript/commit/40719b543d3cde8a62fb6366ee4fc5f1a1ffa028))

## 0.22.0 (2025-05-16)

Full Changelog: [v0.21.0...v0.22.0](https://github.com/groq/groq-typescript/compare/v0.21.0...v0.22.0)

### Features

* **api:** api update ([5899599](https://github.com/groq/groq-typescript/commit/58995997a4cd86506f1fab48aa6436e3aa64a9de))
* **api:** api update ([b346b49](https://github.com/groq/groq-typescript/commit/b346b4955a9f26006b701366083207fce52a7892))
* **api:** api update ([8da1401](https://github.com/groq/groq-typescript/commit/8da1401f0bab59e5df69e862b690ce84c30a2bf8))
* **api:** api update ([c742ce8](https://github.com/groq/groq-typescript/commit/c742ce8c0523590be3a6d38645448caefcfba125))


### Chores

* **ci:** bump node version for release workflows ([b11d937](https://github.com/groq/groq-typescript/commit/b11d937f5ac50f5098d0cae44b2838196c4d5e18))
* fix README example ([13533da](https://github.com/groq/groq-typescript/commit/13533da65012800adf7fa1be76828574ff042ea5))
* GitHub Terraform: Create/Update .github/workflows/stale.yaml [skip ci] ([6ec8975](https://github.com/groq/groq-typescript/commit/6ec8975f7f6c2e5337d08fc74631c0968f979213))


### Documentation

* add examples to tsdocs ([a79829e](https://github.com/groq/groq-typescript/commit/a79829e38f1b585afb1e2b6e3de314226afaffde))
* remove or fix invalid readme examples ([934e30e](https://github.com/groq/groq-typescript/commit/934e30e30879f947cac6245f711b01eb90f63432))

## 0.21.0 (2025-05-01)

Full Changelog: [v0.20.1...v0.21.0](https://github.com/groq/groq-typescript/compare/v0.20.1...v0.21.0)

### Features

* **api:** api update ([d6447d8](https://github.com/groq/groq-typescript/commit/d6447d84e2996341008bb77860aa2b1a1893654f))


### Chores

* **ci:** only use depot for staging repos ([65bbde4](https://github.com/groq/groq-typescript/commit/65bbde447ced600f07cffe4dfeafb975d0d90246))
* **docs:** add missing deprecation warnings ([4fda471](https://github.com/groq/groq-typescript/commit/4fda471aa895a74b17671fabe59566fd17a9a0a4))
* **internal:** codegen related update ([83848bd](https://github.com/groq/groq-typescript/commit/83848bdf4dcb8ce3c8c5bdee11d14d648b7172f4))


### Documentation

* **readme:** fix typo ([7e506b6](https://github.com/groq/groq-typescript/commit/7e506b675dc84636715ad8314118525667e90bb5))

## 0.20.1 (2025-04-23)

Full Changelog: [v0.20.0...v0.20.1](https://github.com/groq/groq-typescript/compare/v0.20.0...v0.20.1)

### Bug Fixes

* **docs:** correct chatCompletion response exmaple in README ([399ccf5](https://github.com/groq/groq-typescript/commit/399ccf55ec7c0847bee32c9e214eecea5b8151c6))


### Chores

* **ci:** add timeout thresholds for CI jobs ([fc2c33e](https://github.com/groq/groq-typescript/commit/fc2c33ef10bb3796501831a7e36c73befadc8306))

## 0.20.0 (2025-04-22)

Full Changelog: [v0.19.0...v0.20.0](https://github.com/groq/groq-typescript/compare/v0.19.0...v0.20.0)

### Features

* **api:** api update ([00b8a36](https://github.com/groq/groq-typescript/commit/00b8a36b9101235badc0a5cf3b19287aaa9e38e3))
* **api:** api update ([b728dc4](https://github.com/groq/groq-typescript/commit/b728dc48b6a7686982dab67238d746276f223602))
* **api:** api update ([055b41b](https://github.com/groq/groq-typescript/commit/055b41bb1ca5e9d64833a3bc54c3afa1f0f16faa))
* **api:** api update ([74fea29](https://github.com/groq/groq-typescript/commit/74fea29aca74cf761a20d0cb4c51b522624a491b))
* **api:** api update ([970e970](https://github.com/groq/groq-typescript/commit/970e970e959ce756dd5194b9f758c10bed4b0485))
* **api:** api update ([cc72162](https://github.com/groq/groq-typescript/commit/cc72162da8f83deb1b8f5d65743c91b5f2fbd7d7))
* **api:** api update ([a945491](https://github.com/groq/groq-typescript/commit/a945491da14e55416dee482f1aac26f1daecfb01))


### Bug Fixes

* **api:** improve type resolution when importing as a package ([#222](https://github.com/groq/groq-typescript/issues/222)) ([45bea8e](https://github.com/groq/groq-typescript/commit/45bea8e84f6dfe75e599aec2b4a5268497914923))
* **client:** send `X-Stainless-Timeout` in seconds ([#219](https://github.com/groq/groq-typescript/issues/219)) ([49bc9d2](https://github.com/groq/groq-typescript/commit/49bc9d2621ff8fe5261c7f22d2283c67ff2883be))
* **mcp:** remove unused tools.ts ([#223](https://github.com/groq/groq-typescript/issues/223)) ([ac43270](https://github.com/groq/groq-typescript/commit/ac43270a148a19cce434c3c3fe7602fbf1b4eccc))


### Chores

* **client:** minor internal fixes ([bfebc04](https://github.com/groq/groq-typescript/commit/bfebc041673e0d23d287601cd694b572aa662b64))
* GitHub Terraform: Create/Update .github/workflows/stale.yaml [skip ci] ([1543aeb](https://github.com/groq/groq-typescript/commit/1543aeb885e0938b5289e477b34fb962754fffa0))
* **internal:** add aliases for Record and Array ([#221](https://github.com/groq/groq-typescript/issues/221)) ([b4d3b51](https://github.com/groq/groq-typescript/commit/b4d3b5143d4cfa6cffd6b2424797feacac3e9229))
* **internal:** reduce CI branch coverage ([48fbdca](https://github.com/groq/groq-typescript/commit/48fbdca40c7127697544df18d6e91bdd52d2ebc9))
* **internal:** upload builds and expand CI branch coverage ([4bbba67](https://github.com/groq/groq-typescript/commit/4bbba6700d7dc0bd03dfa67940e0cc4b293a0213))
* **tests:** improve enum examples ([#224](https://github.com/groq/groq-typescript/issues/224)) ([3639c3b](https://github.com/groq/groq-typescript/commit/3639c3b255fa6e7f97c7a8d747297bc06e58f3f6))

## 0.19.0 (2025-04-02)

Full Changelog: [v0.18.0...v0.19.0](https://github.com/groq/groq-typescript/compare/v0.18.0...v0.19.0)

### Features

* **api:** add batch cancel ([0b0681f](https://github.com/groq/groq-typescript/commit/0b0681ff484dadce7a3f9ab8b5205683e96297fa))


### Chores

* **internal:** skip broken binary tests ([#217](https://github.com/groq/groq-typescript/issues/217)) ([6ad103f](https://github.com/groq/groq-typescript/commit/6ad103f2207357f876734b423c8224d48162c1f6))

## 0.18.0 (2025-04-01)

Full Changelog: [v0.17.0...v0.18.0](https://github.com/groq/groq-typescript/compare/v0.17.0...v0.18.0)

### Features

* **api:** api update ([#208](https://github.com/groq/groq-typescript/issues/208)) ([13e4080](https://github.com/groq/groq-typescript/commit/13e4080afb4a376d9c877cff2b95d3a3d5778b2a))
* **api:** api update ([#209](https://github.com/groq/groq-typescript/issues/209)) ([3b7ed98](https://github.com/groq/groq-typescript/commit/3b7ed98ce896b292f714611424707cc433962ea5))
* **api:** manual updates ([#210](https://github.com/groq/groq-typescript/issues/210)) ([01313a7](https://github.com/groq/groq-typescript/commit/01313a7b71e3127fd1cc1956e9062e4a155c4785))


### Bug Fixes

* avoid type error in certain environments ([#206](https://github.com/groq/groq-typescript/issues/206)) ([e75e67f](https://github.com/groq/groq-typescript/commit/e75e67f4cb8e4d243f78c221fcc58fda3d31b2b3))
* **internal:** work around https://github.com/vercel/next.js/issues/76881 ([#207](https://github.com/groq/groq-typescript/issues/207)) ([4c88261](https://github.com/groq/groq-typescript/commit/4c88261d1a96f255bece611f02cbbc2aec188dc3))


### Chores

* **exports:** cleaner resource index imports ([#204](https://github.com/groq/groq-typescript/issues/204)) ([ddf6d7e](https://github.com/groq/groq-typescript/commit/ddf6d7e0cec0785b100545b9cce44a961b830823))
* **exports:** stop using path fallbacks ([#205](https://github.com/groq/groq-typescript/issues/205)) ([3cad69e](https://github.com/groq/groq-typescript/commit/3cad69e475fa04129276dc3eaea30523df287054))
* **internal:** codegen related update ([#202](https://github.com/groq/groq-typescript/issues/202)) ([870089b](https://github.com/groq/groq-typescript/commit/870089b59e37b6dd34d6a60e37d4da2c980b07cf))

## 0.17.0 (2025-03-19)

Full Changelog: [v0.16.0...v0.17.0](https://github.com/groq/groq-typescript/compare/v0.16.0...v0.17.0)

### Features

* **api:** Add speech endpoint ([#197](https://github.com/groq/groq-typescript/issues/197)) ([a6c574a](https://github.com/groq/groq-typescript/commit/a6c574ae6d36e0f3932c5fbd509dde1369c9d7e6))
* **api:** api update ([#196](https://github.com/groq/groq-typescript/issues/196)) ([555d10a](https://github.com/groq/groq-typescript/commit/555d10ae221de301e41c5634bc1459d411bd66b8))
* **api:** api update ([#198](https://github.com/groq/groq-typescript/issues/198)) ([1b7f534](https://github.com/groq/groq-typescript/commit/1b7f5347e8a8b6f77d11de452f7bf07e291918c8))
* **api:** manual updates ([#190](https://github.com/groq/groq-typescript/issues/190)) ([2afb5be](https://github.com/groq/groq-typescript/commit/2afb5becd0f489f51c51c0fdd58c09b696b3adab))


### Bug Fixes

* **exports:** ensure resource imports don't require /index ([#195](https://github.com/groq/groq-typescript/issues/195)) ([3a54464](https://github.com/groq/groq-typescript/commit/3a544640461fa7e4402ef74181871aa469fc3ad2))


### Chores

* **api:** remove chat_completion_chunk to force a rebuild of it ([#188](https://github.com/groq/groq-typescript/issues/188)) ([8d1e9bf](https://github.com/groq/groq-typescript/commit/8d1e9bf99cda884e6c3312c5d6e3491dd3d3fa53))
* **internal:** remove extra empty newlines ([#194](https://github.com/groq/groq-typescript/issues/194)) ([f054a17](https://github.com/groq/groq-typescript/commit/f054a17f7f8fbbadb8e04d558d71b8967f216156))

## 0.16.0 (2025-03-11)

Full Changelog: [v0.15.0...v0.16.0](https://github.com/groq/groq-typescript/compare/v0.15.0...v0.16.0)

### Features

* add SKIP_BREW env var to ./scripts/bootstrap ([#186](https://github.com/groq/groq-typescript/issues/186)) ([7188745](https://github.com/groq/groq-typescript/commit/71887452485edb042aa49a813ca7295903641c73))
* **client:** accept RFC6838 JSON content types ([#187](https://github.com/groq/groq-typescript/issues/187)) ([8cb3baa](https://github.com/groq/groq-typescript/commit/8cb3baa9f05b4013ffae5e4199ee65d68d7d9b8c))
* **client:** send `X-Stainless-Timeout` header ([#180](https://github.com/groq/groq-typescript/issues/180)) ([a3372bc](https://github.com/groq/groq-typescript/commit/a3372bcccd5b68b6f9610a9e2fc5cf1d30c296d7))


### Bug Fixes

* **client:** fix export map for index exports ([#182](https://github.com/groq/groq-typescript/issues/182)) ([9537350](https://github.com/groq/groq-typescript/commit/9537350b6e19af0c1213ab85f6f2b697863e163d))
* GitHub Terraform: Create/Update .github/workflows/stale.yaml [skip ci] ([9d0bb04](https://github.com/groq/groq-typescript/commit/9d0bb04c153ed9584ac936956fa88c94384f8b63))
* GitHub Terraform: Create/Update .github/workflows/stale.yaml [skip ci] ([5fa6983](https://github.com/groq/groq-typescript/commit/5fa6983323ea6376dfa1eb0f39906b721ea757d6))


### Chores

* **internal:** codegen related update ([#183](https://github.com/groq/groq-typescript/issues/183)) ([203e3b1](https://github.com/groq/groq-typescript/commit/203e3b1102db8515143cdf536f1c9d583b85b3e5))
* **internal:** fix devcontainers setup ([#184](https://github.com/groq/groq-typescript/issues/184)) ([f0df22c](https://github.com/groq/groq-typescript/commit/f0df22cb095ef79933661922c802376f65182fb6))


### Documentation

* update URLs from stainlessapi.com to stainless.com ([#185](https://github.com/groq/groq-typescript/issues/185)) ([1c481a6](https://github.com/groq/groq-typescript/commit/1c481a6fa9fbc3bbe8901470022c2789cbe366e1))

## 0.15.0 (2025-02-05)

Full Changelog: [v0.14.0...v0.15.0](https://github.com/groq/groq-typescript/compare/v0.14.0...v0.15.0)

### Features

* **api:** Add batch API ([#177](https://github.com/groq/groq-typescript/issues/177)) ([0b62a9f](https://github.com/groq/groq-typescript/commit/0b62a9f0e3b53285cf4ca3c8884091626821a406))

## 0.14.0 (2025-02-03)

Full Changelog: [v0.13.0...v0.14.0](https://github.com/groq/groq-typescript/compare/v0.13.0...v0.14.0)

### Features

* **api:** api update ([#174](https://github.com/groq/groq-typescript/issues/174)) ([a8bdb17](https://github.com/groq/groq-typescript/commit/a8bdb179cdefe4fa29a3bcea692c01f166b83b86))

## 0.13.0 (2025-01-29)

Full Changelog: [v0.12.0...v0.13.0](https://github.com/groq/groq-typescript/compare/v0.12.0...v0.13.0)

### Features

* **api:** api update ([#172](https://github.com/groq/groq-typescript/issues/172)) ([f48946a](https://github.com/groq/groq-typescript/commit/f48946a46fccd5313565db3cac6143f536a4c5db))


### Chores

* **internal:** codegen related update ([#168](https://github.com/groq/groq-typescript/issues/168)) ([4d7399b](https://github.com/groq/groq-typescript/commit/4d7399bf51707ebbcdf43897fdd0d3abdfb8db7c))
* **internal:** codegen related update ([#170](https://github.com/groq/groq-typescript/issues/170)) ([2f4ee26](https://github.com/groq/groq-typescript/commit/2f4ee26019b86c3769674d44d7cf3e43c250b6d3))
* **internal:** codegen related update ([#171](https://github.com/groq/groq-typescript/issues/171)) ([99cf580](https://github.com/groq/groq-typescript/commit/99cf580cfdc334df24442319d0e56a4a1e8c7ceb))

## 0.12.0 (2025-01-11)

Full Changelog: [v0.11.0...v0.12.0](https://github.com/groq/groq-typescript/compare/v0.11.0...v0.12.0)

### Features

* **api:** api update ([#166](https://github.com/groq/groq-typescript/issues/166)) ([003600d](https://github.com/groq/groq-typescript/commit/003600d69f9b7a0171d5bb7a09202800d70875a9))


### Chores

* **internal:** codegen related update ([#164](https://github.com/groq/groq-typescript/issues/164)) ([72dfc54](https://github.com/groq/groq-typescript/commit/72dfc54e941be0d385baecd988cc6b0cd2854c19))

## 0.11.0 (2025-01-09)

Full Changelog: [v0.10.0...v0.11.0](https://github.com/groq/groq-typescript/compare/v0.10.0...v0.11.0)

### Features

* **api:** api update ([#161](https://github.com/groq/groq-typescript/issues/161)) ([5628b90](https://github.com/groq/groq-typescript/commit/5628b90da7336889b2d54e2033c065bcd8146e8e))

## 0.10.0 (2025-01-07)

Full Changelog: [v0.9.1...v0.10.0](https://github.com/groq/groq-typescript/compare/v0.9.1...v0.10.0)

### Features

* **api:** api update ([#157](https://github.com/groq/groq-typescript/issues/157)) ([3551087](https://github.com/groq/groq-typescript/commit/35510876e2e5c5295502bec56d09cbf759955bd2))
* **api:** api update ([#159](https://github.com/groq/groq-typescript/issues/159)) ([e0cbb97](https://github.com/groq/groq-typescript/commit/e0cbb978af53e25ba48b768d465c61fd08085f92))


### Bug Fixes

* **client:** normalize method ([#155](https://github.com/groq/groq-typescript/issues/155)) ([c3ba88c](https://github.com/groq/groq-typescript/commit/c3ba88c2f457c057b64331f5153a64d7051a65cf))


### Chores

* **internal:** codegen related update ([#149](https://github.com/groq/groq-typescript/issues/149)) ([4b1115b](https://github.com/groq/groq-typescript/commit/4b1115bc7dad629e6cbfd305d1f02c5d83c08cb4))
* **internal:** codegen related update ([#150](https://github.com/groq/groq-typescript/issues/150)) ([d582c40](https://github.com/groq/groq-typescript/commit/d582c40dfe47d45549a6edd91890ed7822f7af95))
* **internal:** codegen related update ([#151](https://github.com/groq/groq-typescript/issues/151)) ([e153d84](https://github.com/groq/groq-typescript/commit/e153d84e5b9208acfc5b0a368f2e580900f0958d))
* **internal:** codegen related update ([#152](https://github.com/groq/groq-typescript/issues/152)) ([33a4d9c](https://github.com/groq/groq-typescript/commit/33a4d9ce6b56a194001da9d75663e8498083d223))
* **internal:** codegen related update ([#153](https://github.com/groq/groq-typescript/issues/153)) ([dbb9d7f](https://github.com/groq/groq-typescript/commit/dbb9d7fdf457755f2d8a223e667423634ed18cf5))
* **internal:** codegen related update ([#154](https://github.com/groq/groq-typescript/issues/154)) ([579e30e](https://github.com/groq/groq-typescript/commit/579e30e19daaa9b0aaae2f9d5f268c7983f74376))
* **internal:** codegen related update ([#156](https://github.com/groq/groq-typescript/issues/156)) ([05f714b](https://github.com/groq/groq-typescript/commit/05f714b65c1251938d4d75a25e319a75907cf9d0))
* **internal:** codegen related update ([#158](https://github.com/groq/groq-typescript/issues/158)) ([265305a](https://github.com/groq/groq-typescript/commit/265305a5b3746b357b0bd2fe45080b373f2d1d7a))
* **internal:** fix some typos ([#147](https://github.com/groq/groq-typescript/issues/147)) ([476a620](https://github.com/groq/groq-typescript/commit/476a62017698e9b766d1ac0657e1dbdfd7af0cea))

## 0.9.1 (2024-12-12)

Full Changelog: [v0.9.0...v0.9.1](https://github.com/groq/groq-typescript/compare/v0.9.0...v0.9.1)

### Chores

* **internal:** remove unnecessary getRequestClient function ([#141](https://github.com/groq/groq-typescript/issues/141)) ([05ccf25](https://github.com/groq/groq-typescript/commit/05ccf251bb1e1bf0afc3af5572f4faf20ebd1410))
* **internal:** update isAbsoluteURL ([#145](https://github.com/groq/groq-typescript/issues/145)) ([18e414e](https://github.com/groq/groq-typescript/commit/18e414ed562876eb1dadff97a79aa2bbe90ff0ab))
* **types:** nicer error class types + jsdocs ([#144](https://github.com/groq/groq-typescript/issues/144)) ([860655e](https://github.com/groq/groq-typescript/commit/860655eb638047c681ed602002e1de8dfacb7d3d))

## 0.9.0 (2024-12-03)

Full Changelog: [v0.8.0...v0.9.0](https://github.com/groq/groq-typescript/compare/v0.8.0...v0.9.0)

### Features

* **api:** api update ([#133](https://github.com/groq/groq-typescript/issues/133)) ([9a22d20](https://github.com/groq/groq-typescript/commit/9a22d20b157fa1670d24cc0efae40081e53bfbd0))
* chore(deps): bump cross-spawn from 7.0.3 to 7.0.6 ([#138](https://github.com/groq/groq-typescript/issues/138)) ([ba61485](https://github.com/groq/groq-typescript/commit/ba61485623efcb4413b6019c48dbfaf9eec053cf))
* **internal:** make git install file structure match npm ([#137](https://github.com/groq/groq-typescript/issues/137)) ([0ae2b87](https://github.com/groq/groq-typescript/commit/0ae2b87c484394211a8f1df81a0a86fdb2082521))


### Chores

* **internal:** version bump ([#131](https://github.com/groq/groq-typescript/issues/131)) ([7ba4fdf](https://github.com/groq/groq-typescript/commit/7ba4fdf0782fd0525e14c1a3ac9df3379579ca55))
* rebuild project due to codegen change ([#134](https://github.com/groq/groq-typescript/issues/134)) ([e2711d6](https://github.com/groq/groq-typescript/commit/e2711d609491d4ba1b72db5844449e8aec18f80c))
* remove redundant word in comment ([#136](https://github.com/groq/groq-typescript/issues/136)) ([0b0cd01](https://github.com/groq/groq-typescript/commit/0b0cd01015cd2516c5d1b72f4ad495babacdb794))


### Documentation

* remove suggestion to use `npm` call out ([#135](https://github.com/groq/groq-typescript/issues/135)) ([29cb7b1](https://github.com/groq/groq-typescript/commit/29cb7b1b26f02c5284873da6380bbfe3e4099d88))

## 0.8.0 (2024-11-09)

Full Changelog: [v0.7.0...v0.8.0](https://github.com/groq/groq-typescript/compare/v0.7.0...v0.8.0)

### Features

* **api:** api update ([#127](https://github.com/groq/groq-typescript/issues/127)) ([5d82d2d](https://github.com/groq/groq-typescript/commit/5d82d2dd7119c17fdae3618b06c607b17ee00d67))
* **api:** api update ([#128](https://github.com/groq/groq-typescript/issues/128)) ([ae555c1](https://github.com/groq/groq-typescript/commit/ae555c1c6934b589e862ebd6b684cede0f09fb18))


### Bug Fixes

* **client:** correct File construction from node-fetch Responses ([#125](https://github.com/groq/groq-typescript/issues/125)) ([42a63ac](https://github.com/groq/groq-typescript/commit/42a63ac2bbf51fde1f61d94a61011d9a49822552))
* GitHub Terraform: Create/Update .github/workflows/stale.yaml [skip ci] ([ab82c0a](https://github.com/groq/groq-typescript/commit/ab82c0a3ba00e63a92a88e24d25e4e41ec090951))


### Chores

* **ci:** install deps via ./script/bootstrap ([#124](https://github.com/groq/groq-typescript/issues/124)) ([edc34d0](https://github.com/groq/groq-typescript/commit/edc34d08c51c8cae2ebbc32296dc9069a6547384))
* rebuild project due to codegen change ([#130](https://github.com/groq/groq-typescript/issues/130)) ([58bc1e8](https://github.com/groq/groq-typescript/commit/58bc1e81f7e89108cada06cf762b8de923849078))
* run tsc as part of lint script ([#122](https://github.com/groq/groq-typescript/issues/122)) ([85a3a56](https://github.com/groq/groq-typescript/commit/85a3a5617b69f87c8dcb4641e39b7302b7b9cfae))

## 0.7.0 (2024-09-03)

Full Changelog: [v0.6.1...v0.7.0](https://github.com/groq/groq-typescript/compare/v0.6.1...v0.7.0)

### Features

* **internal:** handle streaming error ([8f8f4eb](https://github.com/groq/groq-typescript/commit/8f8f4eb29c73229d2ffaa388c5d608291331c731))


### Chores

* **ci:** check for build errors ([#118](https://github.com/groq/groq-typescript/issues/118)) ([a784f84](https://github.com/groq/groq-typescript/commit/a784f842dc9785a127925da48c85531832c5d19b))

## 0.6.1 (2024-08-28)

Full Changelog: [v0.6.0...v0.6.1](https://github.com/groq/groq-typescript/compare/v0.6.0...v0.6.1)

### Chores

* **deps:** bump micromatch from 4.0.5 to 4.0.8 ([8cddd91](https://github.com/groq/groq-typescript/commit/8cddd9177aae4ab6b55b6e6e35ebb31bb074ce1e))

## 0.6.0 (2024-08-27)

Full Changelog: [v0.5.0...v0.6.0](https://github.com/groq/groq-typescript/compare/v0.5.0...v0.6.0)

### Features

* **api:** OpenAPI spec update via Stainless API ([#101](https://github.com/groq/groq-typescript/issues/101)) ([e2db04a](https://github.com/groq/groq-typescript/commit/e2db04a02b60bdf26a9590916f446250742cef8c))
* **api:** OpenAPI spec update via Stainless API ([#104](https://github.com/groq/groq-typescript/issues/104)) ([c08717b](https://github.com/groq/groq-typescript/commit/c08717ba3bbae8ea713f70672da295261fc26a28))
* **api:** OpenAPI spec update via Stainless API ([#110](https://github.com/groq/groq-typescript/issues/110)) ([c194ccd](https://github.com/groq/groq-typescript/commit/c194ccd2b3f2a5814892046af653a428fb3b1cfb))
* **api:** OpenAPI spec update via Stainless API ([#112](https://github.com/groq/groq-typescript/issues/112)) ([0b0cb13](https://github.com/groq/groq-typescript/commit/0b0cb138107945607fc3abef23e1ef2e355d748a))
* **api:** update via SDK Studio ([#99](https://github.com/groq/groq-typescript/issues/99)) ([e96de18](https://github.com/groq/groq-typescript/commit/e96de183786c91172ad59225f32b23265c9a7fb6))


### Bug Fixes

* **compat:** remove ReadableStream polyfill redundant since node v16 ([#109](https://github.com/groq/groq-typescript/issues/109)) ([90c2d43](https://github.com/groq/groq-typescript/commit/90c2d4340def37b2ad379ea838e63305e9dd4405))


### Chores

* **ci:** limit release doctor target branches ([#107](https://github.com/groq/groq-typescript/issues/107)) ([1409094](https://github.com/groq/groq-typescript/commit/1409094d1e87412fff2615f17783457ef815d088))
* **deps:** bump braces from 3.0.2 to 3.0.3 ([fedebb4](https://github.com/groq/groq-typescript/commit/fedebb4f2dfa2538db9dfdee47ba0581434ffc84))
* **docs:** use client instead of package name in Node examples ([#106](https://github.com/groq/groq-typescript/issues/106)) ([7279938](https://github.com/groq/groq-typescript/commit/727993848991d1fde8412a57d7781bdf4bd71641))
* **internal:** codegen related update ([#105](https://github.com/groq/groq-typescript/issues/105)) ([7b53f7c](https://github.com/groq/groq-typescript/commit/7b53f7cfa86fc0f4fc779bbc3fd112252b9d5260))
* **internal:** codegen related update ([#111](https://github.com/groq/groq-typescript/issues/111)) ([d81d781](https://github.com/groq/groq-typescript/commit/d81d7817f4c6c69f2ea91ff62c25b9162b4267bd))
* **tests:** update prism version ([#108](https://github.com/groq/groq-typescript/issues/108)) ([a2528da](https://github.com/groq/groq-typescript/commit/a2528da81262dcba2bd577af3f63166a43092a86))

## 0.5.0 (2024-06-11)

Full Changelog: [v0.4.0...v0.5.0](https://github.com/groq/groq-typescript/compare/v0.4.0...v0.5.0)

### Features

* **api:** Fix audio transcription response formats ([7154e35](https://github.com/groq/groq-typescript/commit/7154e35221cb906b853c0271ec4badeeef7c825b))
* **api:** Tool calling features ([e257ab7](https://github.com/groq/groq-typescript/commit/e257ab70890b4c8be0879696edf7379f5225566b))

## 0.4.0 (2024-05-23)

Full Changelog: [v0.3.3...v0.4.0](https://github.com/groq/groq-typescript/compare/v0.3.3...v0.4.0)

### Features

* **api:** Add embeddings endpoint ([cf59ec3](https://github.com/groq/groq-typescript/commit/cf59ec37bff37cb923eb389126f17931fcf97e2e))
* **api:** Add support for image_url in chat user messages ([a8f7743](https://github.com/groq/groq-typescript/commit/a8f7743e3663de628247df3a655938b3ed53231a))
* **api:** Define OpenAI-compatible models ([29fe116](https://github.com/groq/groq-typescript/commit/29fe116c88ad0d3c28562581f0929090833861ad))
* **api:** Improve types ([c879cb2](https://github.com/groq/groq-typescript/commit/c879cb29871aa247a60b984874ffca40a9ae924c))


### Bug Fixes

* patch streaming ([80b1255](https://github.com/groq/groq-typescript/commit/80b12555fcffd58bfd760b993e8bc3dcebfdbe6b))


### Chores

* **api:** add response objects for translations and transcriptions ([ceba2a3](https://github.com/groq/groq-typescript/commit/ceba2a3c7a398c25cd47f6cc42f655822877c53a))
* **api:** Internal SDK changes ([e1a6688](https://github.com/groq/groq-typescript/commit/e1a66880ec8843f5b9e62526ed31fbe34345a293))

## 0.3.3 (2024-04-29)

Full Changelog: [v0.3.2...v0.3.3](https://github.com/groq/groq-typescript/compare/v0.3.2...v0.3.3)

### Bug Fixes

* update import in chat_completions_ext.ts to address TS compilation error ([b1de786](https://github.com/groq/groq-typescript/commit/b1de7862495bd76b50b9b596d94bd5d7f2143f57))

## 0.3.2 (2024-03-08)

Full Changelog: [v0.3.1...v0.3.2](https://github.com/groq/groq-typescript/compare/v0.3.1...v0.3.2)

### Features

* Add transcription and translation endpoints ([5a422c4](https://github.com/groq/groq-typescript/commit/5a422c4f2c4d92d5a525ee63d674c92aff4990bb))


### Bug Fixes

* use absolute paths in /src/lib ([5c961f7](https://github.com/groq/groq-typescript/commit/5c961f7233ce5e4cfd32d3edcb9a845df7014b89))


### Chores

* Add CODEOWNERS ([e995ed1](https://github.com/groq/groq-typescript/commit/e995ed1214237379a1e4819a61876113e81a456b))
* Fix streaming before release ([b7463c5](https://github.com/groq/groq-typescript/commit/b7463c5690613c2142e10f3eb549e87471aea367))

## 0.3.1 (2024-03-01)

Full Changelog: [v0.3.0...v0.3.1](https://github.com/groq/groq-typescript/compare/v0.3.0...v0.3.1)

### Features

* OpenAPI spec update via Stainless API ([#11](https://github.com/groq/groq-typescript/issues/11)) ([3b8d249](https://github.com/groq/groq-typescript/commit/3b8d249e4bb70d960447bb1667b59e30baec1853))

## 0.3.0 (2024-02-21)

Full Changelog: [v0.2.1...v0.3.0](https://github.com/groq/groq-typescript/compare/v0.2.1...v0.3.0)

### Features

* update via SDK Studio ([#10](https://github.com/groq/groq-typescript/issues/10)) ([b9b5d6a](https://github.com/groq/groq-typescript/commit/b9b5d6af6cc2a37f404ec6685a9b950d9703cec5))
* update via SDK Studio ([#5](https://github.com/groq/groq-typescript/issues/5)) ([d6202e9](https://github.com/groq/groq-typescript/commit/d6202e9b12fa79c69fbe65c58fc716cd464f52f0))
* update via SDK Studio ([#8](https://github.com/groq/groq-typescript/issues/8)) ([cbbbe5b](https://github.com/groq/groq-typescript/commit/cbbbe5b357ff8673b3d310010d4c66ff5389bfa2))


### Chores

* update branch ([#7](https://github.com/groq/groq-typescript/issues/7)) ([5bd9088](https://github.com/groq/groq-typescript/commit/5bd9088546ebcfc0f857a3630fecf35f714d53a9))

## 0.2.0 (2024-02-15)

Full Changelog: [v0.1.0...v0.2.0](https://github.com/groq/groq-node/compare/v0.1.0...v0.2.0)

### Features

* Add initial Stainless SDK ([a6c643b](https://github.com/groq/groq-node/commit/a6c643bfc3b59145b1441fe927798de63b935992))
* create default branch ([48105eb](https://github.com/groq/groq-node/commit/48105eb898d6cc13c9fdd7e96989d657d9d30f4e))
* update via SDK Studio ([#3](https://github.com/groq/groq-node/issues/3)) ([c99373c](https://github.com/groq/groq-node/commit/c99373cdfd6b94a9d9346cf717a5354b378dc2f1))


### Chores

* go live ([#1](https://github.com/groq/groq-node/issues/1)) ([08d2551](https://github.com/groq/groq-node/commit/08d2551190a07ff8619ddc85a083eb130a33ff0b))

## 0.1.0 (2024-02-12)

Full Changelog: [v0.0.1...v0.1.0](https://github.com/definitive-io/groqcloud-node/compare/v0.0.1...v0.1.0)

### Features

* Add initial Stainless SDK ([73f0686](https://github.com/definitive-io/groqcloud-node/commit/73f0686f4dc84d332cf0eb072cad15dbd8594dea))
* create default branch ([9a37669](https://github.com/definitive-io/groqcloud-node/commit/9a376697847c0e7d9463cf5ad55ff469f59577cb))
* OpenAPI spec update ([#1](https://github.com/definitive-io/groqcloud-node/issues/1)) ([9ab0d58](https://github.com/definitive-io/groqcloud-node/commit/9ab0d580b2c9fa77b8e60a61e7711c605cb24f48))
* OpenAPI spec update ([#10](https://github.com/definitive-io/groqcloud-node/issues/10)) ([66870af](https://github.com/definitive-io/groqcloud-node/commit/66870afc708e452df9d75487cfec39c73c883adf))
* OpenAPI spec update ([#11](https://github.com/definitive-io/groqcloud-node/issues/11)) ([af63094](https://github.com/definitive-io/groqcloud-node/commit/af630946df953c20cb6ddd9dbbcef70a016c4147))
* OpenAPI spec update ([#12](https://github.com/definitive-io/groqcloud-node/issues/12)) ([8f55f00](https://github.com/definitive-io/groqcloud-node/commit/8f55f00afa2efe4d41a969ac92cc7302686ea9fe))
* OpenAPI spec update ([#13](https://github.com/definitive-io/groqcloud-node/issues/13)) ([ec65a86](https://github.com/definitive-io/groqcloud-node/commit/ec65a86b21d632d8785ea86c83d9ea8fbd49997b))
* OpenAPI spec update ([#3](https://github.com/definitive-io/groqcloud-node/issues/3)) ([18ca5ad](https://github.com/definitive-io/groqcloud-node/commit/18ca5ad6efa200b8dc000603a3c1d2c4b64a1a7c))
* OpenAPI spec update ([#4](https://github.com/definitive-io/groqcloud-node/issues/4)) ([7d3354a](https://github.com/definitive-io/groqcloud-node/commit/7d3354ad7a086e6b935934d09052086e9ca5161b))
* OpenAPI spec update ([#5](https://github.com/definitive-io/groqcloud-node/issues/5)) ([8a029ec](https://github.com/definitive-io/groqcloud-node/commit/8a029ec0f29d7a544d4c6ad88ed2d5856df77ed8))
* OpenAPI spec update ([#6](https://github.com/definitive-io/groqcloud-node/issues/6)) ([d45a00d](https://github.com/definitive-io/groqcloud-node/commit/d45a00d6d2d7ec67302e5bce0eba89d85998dd75))
* OpenAPI spec update ([#7](https://github.com/definitive-io/groqcloud-node/issues/7)) ([9142dad](https://github.com/definitive-io/groqcloud-node/commit/9142dade7bb345f47e348f300be2fd7beaa7e7d7))
* OpenAPI spec update ([#8](https://github.com/definitive-io/groqcloud-node/issues/8)) ([d143ade](https://github.com/definitive-io/groqcloud-node/commit/d143ade1cec62b4261d1661f62cb7c88f2cd22fb))
* OpenAPI spec update ([#9](https://github.com/definitive-io/groqcloud-node/issues/9)) ([2849be2](https://github.com/definitive-io/groqcloud-node/commit/2849be292711a6f8bf597458aa80e37ecc8e9716))
