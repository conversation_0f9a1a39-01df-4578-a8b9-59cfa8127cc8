"""Financial functions for the AI pipeline."""

from typing import Any, Dict, List, Optional
from src.models.schemas import FunctionSchema, FunctionParameter, FunctionParameterType
from datetime import datetime


def retrieve_invoices(month: str = None, year: int = None, status: str = None) -> List[Dict[str, Any]]:
    """Retrieve invoices with optional filtering."""
    # Mock invoice data
    invoices = [
        {"id": "INV-001", "amount": 1500.00, "date": "2024-03-05", "status": "paid", "customer": "ACME Corp"},
        {"id": "INV-002", "amount": 2300.00, "date": "2024-03-12", "status": "pending", "customer": "Tech Solutions"},
        {"id": "INV-003", "amount": 850.00, "date": "2024-03-20", "status": "paid", "customer": "StartupXYZ"}
    ]
    
    # Apply filters
    result = invoices.copy()
    if month:
        result = [inv for inv in result if month.lower() in inv["date"].lower()]
    if year:
        result = [inv for inv in result if str(year) in inv["date"]]
    if status:
        result = [inv for inv in result if inv["status"] == status]
    
    return result


def calculate_total_amount(invoices: List[Dict[str, Any]], field: str = "amount") -> float:
    """Calculate total amount from invoice data."""
    return sum(invoice.get(field, 0) for invoice in invoices)


def generate_invoice(customer: str, items: List[Dict[str, Any]], due_date: str) -> Dict[str, Any]:
    """Generate a new invoice."""
    total = sum(item.get("amount", 0) for item in items)
    return {
        "success": True,
        "invoice_id": f"INV-{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "customer": customer,
        "items_count": len(items),
        "total_amount": total,
        "due_date": due_date,
        "status": "draft",
        "created_at": datetime.now().isoformat()
    }


def process_payment(invoice_id: str, amount: float, payment_method: str) -> Dict[str, Any]:
    """Process a payment for an invoice."""
    return {
        "success": True,
        "payment_id": f"PAY-{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "invoice_id": invoice_id,
        "amount": amount,
        "payment_method": payment_method,
        "status": "completed",
        "processed_at": datetime.now().isoformat()
    }


def generate_financial_report(period: str, report_type: str) -> Dict[str, Any]:
    """Generate financial reports."""
    return {
        "success": True,
        "report_id": f"RPT-{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "period": period,
        "report_type": report_type,
        "total_revenue": 15750.00,
        "total_expenses": 8200.00,
        "net_profit": 7550.00,
        "generated_at": datetime.now().isoformat()
    }


def calculate_tax(amount: float, tax_rate: float, tax_type: str = "sales") -> Dict[str, Any]:
    """Calculate tax on an amount."""
    tax_amount = amount * (tax_rate / 100)
    return {
        "base_amount": amount,
        "tax_rate": tax_rate,
        "tax_type": tax_type,
        "tax_amount": tax_amount,
        "total_amount": amount + tax_amount
    }


def track_expenses(category: str, amount: float, description: str, date: str = None) -> Dict[str, Any]:
    """Track business expenses."""
    return {
        "success": True,
        "expense_id": f"EXP-{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "category": category,
        "amount": amount,
        "description": description,
        "date": date or datetime.now().strftime('%Y-%m-%d'),
        "recorded_at": datetime.now().isoformat()
    }


def register_functions(registry):
    """Register financial functions."""
    
    registry.register_function(
        FunctionSchema(name="retrieve_invoices", description="Retrieve invoices with filtering", category="financial",
                      parameters=[FunctionParameter(name="month", type=FunctionParameterType.STRING, description="Filter by month", required=False),
                                FunctionParameter(name="year", type=FunctionParameterType.INTEGER, description="Filter by year", required=False),
                                FunctionParameter(name="status", type=FunctionParameterType.STRING, description="Filter by status", required=False, enum_values=["draft", "sent", "paid", "overdue", "cancelled"])],
                      returns="List of invoices", examples=["retrieve_invoices('March', 2024, 'paid')"]),
        retrieve_invoices
    )
    
    registry.register_function(
        FunctionSchema(name="calculate_total_amount", description="Calculate total amount from invoices", category="financial",
                      parameters=[FunctionParameter(name="invoices", type=FunctionParameterType.LIST, description="Invoice data"),
                                FunctionParameter(name="field", type=FunctionParameterType.STRING, description="Field to sum", required=False, default="amount")],
                      returns="Total amount", examples=["calculate_total_amount(invoices, 'amount')"]),
        calculate_total_amount
    )
    
    registry.register_function(
        FunctionSchema(name="generate_invoice", description="Generate new invoice", category="financial",
                      parameters=[FunctionParameter(name="customer", type=FunctionParameterType.STRING, description="Customer name"),
                                FunctionParameter(name="items", type=FunctionParameterType.LIST, description="Invoice items"),
                                FunctionParameter(name="due_date", type=FunctionParameterType.STRING, description="Due date (YYYY-MM-DD)")],
                      returns="Invoice generation result", examples=["generate_invoice('ACME Corp', [{'item': 'Service', 'amount': 1000}], '2024-04-15')"]),
        generate_invoice
    )
    
    registry.register_function(
        FunctionSchema(name="process_payment", description="Process payment for invoice", category="financial",
                      parameters=[FunctionParameter(name="invoice_id", type=FunctionParameterType.STRING, description="Invoice ID"),
                                FunctionParameter(name="amount", type=FunctionParameterType.FLOAT, description="Payment amount"),
                                FunctionParameter(name="payment_method", type=FunctionParameterType.STRING, description="Payment method", enum_values=["credit_card", "bank_transfer", "check", "cash"])],
                      returns="Payment processing result", examples=["process_payment('INV-001', 1500.00, 'credit_card')"]),
        process_payment
    )
    
    registry.register_function(
        FunctionSchema(name="generate_financial_report", description="Generate financial reports", category="financial",
                      parameters=[FunctionParameter(name="period", type=FunctionParameterType.STRING, description="Report period"),
                                FunctionParameter(name="report_type", type=FunctionParameterType.STRING, description="Report type", enum_values=["income", "expense", "profit_loss", "balance_sheet", "cash_flow"])],
                      returns="Financial report", examples=["generate_financial_report('Q1 2024', 'profit_loss')"]),
        generate_financial_report
    )
    
    registry.register_function(
        FunctionSchema(name="calculate_tax", description="Calculate tax on amount", category="financial",
                      parameters=[FunctionParameter(name="amount", type=FunctionParameterType.FLOAT, description="Base amount"),
                                FunctionParameter(name="tax_rate", type=FunctionParameterType.FLOAT, description="Tax rate percentage"),
                                FunctionParameter(name="tax_type", type=FunctionParameterType.STRING, description="Tax type", required=False, default="sales", enum_values=["sales", "income", "property", "vat"])],
                      returns="Tax calculation result", examples=["calculate_tax(1000.00, 8.5, 'sales')"]),
        calculate_tax
    )
    
    registry.register_function(
        FunctionSchema(name="track_expenses", description="Track business expenses", category="financial",
                      parameters=[FunctionParameter(name="category", type=FunctionParameterType.STRING, description="Expense category"),
                                FunctionParameter(name="amount", type=FunctionParameterType.FLOAT, description="Expense amount"),
                                FunctionParameter(name="description", type=FunctionParameterType.STRING, description="Expense description"),
                                FunctionParameter(name="date", type=FunctionParameterType.STRING, description="Expense date", required=False)],
                      returns="Expense tracking result", examples=["track_expenses('Office Supplies', 150.00, 'Printer paper and ink')"]),
        track_expenses
    )
