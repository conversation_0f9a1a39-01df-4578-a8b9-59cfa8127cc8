#!/usr/bin/env python3
"""
Simple server startup for the AI Function Calling Pipeline.
"""

import sys
import os
from pathlib import Path

# Setup paths
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))
os.chdir(project_root)

print("🚀 AI Function Calling Pipeline Server")
print("=====================================")

try:
    # Import and test components
    from src.api.routes import app
    import uvicorn
    
    print("✅ All components loaded successfully")
    print("🌐 Starting server on http://127.0.0.1:8000")
    print("📚 API docs will be at http://127.0.0.1:8000/docs")
    print("⚡ Press Ctrl+C to stop the server")
    print()
    
    # Start the server
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8000,
        log_level="info",
        access_log=False
    )
    
except KeyboardInterrupt:
    print("\n👋 Server stopped")
except Exception as e:
    print(f"❌ Error starting server: {e}")
    import traceback
    traceback.print_exc()
