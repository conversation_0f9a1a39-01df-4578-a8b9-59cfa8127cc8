{"total_functions": 60, "categories": ["data_operations", "communication", "file_operations", "web_operations", "analytics", "database", "scheduling", "financial", "system_operations"], "functions_by_category": {"data_operations": [{"name": "retrieve_data", "description": "Retrieve data from a specified source with optional filtering", "category": "data_operations", "parameters": [{"name": "source", "type": "string", "description": "Data source identifier", "required": true, "default": null, "enum_values": null}, {"name": "filters", "type": "dict", "description": "Filter conditions", "required": false, "default": null, "enum_values": null}, {"name": "limit", "type": "integer", "description": "Maximum number of records", "required": false, "default": null, "enum_values": null}], "returns": "List of data records", "examples": ["retrieve_data('invoices', {'month': 'March'}, 100)"]}, {"name": "filter_data", "description": "Filter data based on specified conditions", "category": "data_operations", "parameters": [{"name": "data", "type": "list", "description": "Data to filter", "required": true, "default": null, "enum_values": null}, {"name": "conditions", "type": "dict", "description": "Filter conditions", "required": true, "default": null, "enum_values": null}], "returns": "Filtered data list", "examples": ["filter_data(data, {'status': 'active'})"]}, {"name": "sort_data", "description": "Sort data by a specified field", "category": "data_operations", "parameters": [{"name": "data", "type": "list", "description": "Data to sort", "required": true, "default": null, "enum_values": null}, {"name": "sort_by", "type": "string", "description": "Field to sort by", "required": true, "default": null, "enum_values": null}, {"name": "ascending", "type": "boolean", "description": "Sort order", "required": false, "default": true, "enum_values": null}], "returns": "Sorted data list", "examples": ["sort_data(data, 'date', False)"]}, {"name": "group_data", "description": "Group data by a specified field", "category": "data_operations", "parameters": [{"name": "data", "type": "list", "description": "Data to group", "required": true, "default": null, "enum_values": null}, {"name": "group_by", "type": "string", "description": "Field to group by", "required": true, "default": null, "enum_values": null}], "returns": "Dictionary of grouped data", "examples": ["group_data(data, 'category')"]}, {"name": "aggregate_data", "description": "Aggregate data using specified operation", "category": "data_operations", "parameters": [{"name": "data", "type": "list", "description": "Data to aggregate", "required": true, "default": null, "enum_values": null}, {"name": "field", "type": "string", "description": "Field to aggregate", "required": true, "default": null, "enum_values": null}, {"name": "operation", "type": "string", "description": "Aggregation operation", "required": true, "default": null, "enum_values": ["sum", "avg", "count", "min", "max"]}], "returns": "Aggregated value", "examples": ["aggregate_data(invoices, 'amount', 'sum')"]}, {"name": "transform_data", "description": "Transform data fields based on specified transformations", "category": "data_operations", "parameters": [{"name": "data", "type": "list", "description": "Data to transform", "required": true, "default": null, "enum_values": null}, {"name": "transformations", "type": "dict", "description": "Field transformations", "required": true, "default": null, "enum_values": null}], "returns": "Transformed data list", "examples": ["transform_data(data, {'name': 'uppercase', 'amount': 'round'})"]}, {"name": "merge_data", "description": "Merge two datasets on a common key", "category": "data_operations", "parameters": [{"name": "data1", "type": "list", "description": "First dataset", "required": true, "default": null, "enum_values": null}, {"name": "data2", "type": "list", "description": "Second dataset", "required": true, "default": null, "enum_values": null}, {"name": "join_key", "type": "string", "description": "Common key for joining", "required": true, "default": null, "enum_values": null}], "returns": "Merged data list", "examples": ["merge_data(customers, orders, 'customer_id')"]}, {"name": "deduplicate_data", "description": "Remove duplicate entries from data", "category": "data_operations", "parameters": [{"name": "data", "type": "list", "description": "Data to deduplicate", "required": true, "default": null, "enum_values": null}, {"name": "key", "type": "string", "description": "Key field for deduplication", "required": false, "default": null, "enum_values": null}], "returns": "Deduplicated data list", "examples": ["deduplicate_data(data, 'id')"]}], "communication": [{"name": "send_email", "description": "Send an email to specified recipients", "category": "communication", "parameters": [{"name": "to", "type": "string", "description": "Recipient email address", "required": true, "default": null, "enum_values": null}, {"name": "subject", "type": "string", "description": "Email subject", "required": true, "default": null, "enum_values": null}, {"name": "body", "type": "string", "description": "Email body content", "required": true, "default": null, "enum_values": null}, {"name": "cc", "type": "list", "description": "CC recipients", "required": false, "default": null, "enum_values": null}, {"name": "attachments", "type": "list", "description": "File attachments", "required": false, "default": null, "enum_values": null}], "returns": "Email sending result with message ID and status", "examples": ["send_email('<EMAIL>', 'Monthly Report', 'Please find the report attached.')"]}, {"name": "send_sms", "description": "Send SMS to a phone number", "category": "communication", "parameters": [{"name": "to", "type": "string", "description": "Recipient phone number", "required": true, "default": null, "enum_values": null}, {"name": "message", "type": "string", "description": "SMS message content", "required": true, "default": null, "enum_values": null}], "returns": "SMS sending result with message ID and status", "examples": ["send_sms('+1234567890', 'Your order has been shipped!')"]}, {"name": "send_notification", "description": "Send a notification through specified channels", "category": "communication", "parameters": [{"name": "title", "type": "string", "description": "Notification title", "required": true, "default": null, "enum_values": null}, {"name": "message", "type": "string", "description": "Notification message", "required": true, "default": null, "enum_values": null}, {"name": "priority", "type": "string", "description": "Notification priority", "required": false, "default": "normal", "enum_values": ["low", "normal", "high", "urgent"]}, {"name": "channels", "type": "list", "description": "Notification channels", "required": false, "default": null, "enum_values": null}], "returns": "Notification sending result", "examples": ["send_notification('System Alert', 'Database backup completed', 'normal', ['slack', 'email'])"]}, {"name": "create_message_template", "description": "Create a reusable message template", "category": "communication", "parameters": [{"name": "name", "type": "string", "description": "Template name", "required": true, "default": null, "enum_values": null}, {"name": "template", "type": "string", "description": "Message template with variables", "required": true, "default": null, "enum_values": null}, {"name": "variables", "type": "list", "description": "List of template variables", "required": true, "default": null, "enum_values": null}], "returns": "Created template information", "examples": ["create_message_template('welcome', 'Hello {name}, welcome to {company}!', ['name', 'company'])"]}, {"name": "send_templated_message", "description": "Send a message using a predefined template", "category": "communication", "parameters": [{"name": "template_id", "type": "string", "description": "Template identifier", "required": true, "default": null, "enum_values": null}, {"name": "recipient", "type": "string", "description": "Message recipient", "required": true, "default": null, "enum_values": null}, {"name": "variables", "type": "dict", "description": "Template variable values", "required": true, "default": null, "enum_values": null}, {"name": "channel", "type": "string", "description": "Communication channel", "required": false, "default": "email", "enum_values": null}], "returns": "Message sending result", "examples": ["send_templated_message('welcome_template', '<EMAIL>', {'name': '<PERSON>', 'company': 'ACME'})"]}, {"name": "send_bulk_messages", "description": "Send the same message to multiple recipients", "category": "communication", "parameters": [{"name": "recipients", "type": "list", "description": "List of recipients", "required": true, "default": null, "enum_values": null}, {"name": "message", "type": "string", "description": "Message content", "required": true, "default": null, "enum_values": null}, {"name": "channel", "type": "string", "description": "Communication channel", "required": false, "default": "email", "enum_values": null}], "returns": "Bulk sending result with statistics", "examples": ["send_bulk_messages(['<EMAIL>', '<EMAIL>'], 'Newsletter content')"]}, {"name": "schedule_message", "description": "Schedule a message to be sent at a specific time", "category": "communication", "parameters": [{"name": "recipient", "type": "string", "description": "Message recipient", "required": true, "default": null, "enum_values": null}, {"name": "message", "type": "string", "description": "Message content", "required": true, "default": null, "enum_values": null}, {"name": "send_at", "type": "string", "description": "Scheduled send time (ISO format)", "required": true, "default": null, "enum_values": null}, {"name": "channel", "type": "string", "description": "Communication channel", "required": false, "default": "email", "enum_values": null}], "returns": "Scheduled message information", "examples": ["schedule_message('<EMAIL>', 'Reminder: Meeting tomorrow', '2024-03-15T09:00:00')"]}, {"name": "get_message_status", "description": "Get the delivery status of a message", "category": "communication", "parameters": [{"name": "message_id", "type": "string", "description": "Message identifier", "required": true, "default": null, "enum_values": null}], "returns": "Message status information", "examples": ["get_message_status('email_20240315_143022')"]}], "file_operations": [{"name": "read_file", "description": "Read content from a file", "category": "file_operations", "parameters": [{"name": "file_path", "type": "string", "description": "Path to the file", "required": true, "default": null, "enum_values": null}, {"name": "encoding", "type": "string", "description": "File encoding", "required": false, "default": "utf-8", "enum_values": null}], "returns": "File content as string", "examples": ["read_file('/path/to/document.txt')"]}, {"name": "write_file", "description": "Write content to a file", "category": "file_operations", "parameters": [{"name": "file_path", "type": "string", "description": "Path to the file", "required": true, "default": null, "enum_values": null}, {"name": "content", "type": "string", "description": "Content to write", "required": true, "default": null, "enum_values": null}, {"name": "encoding", "type": "string", "description": "File encoding", "required": false, "default": "utf-8", "enum_values": null}, {"name": "append", "type": "boolean", "description": "Append to file", "required": false, "default": false, "enum_values": null}], "returns": "Write operation result", "examples": ["write_file('/path/to/output.txt', 'Hello World')"]}, {"name": "copy_file", "description": "Copy a file from source to destination", "category": "file_operations", "parameters": [{"name": "source", "type": "string", "description": "Source file path", "required": true, "default": null, "enum_values": null}, {"name": "destination", "type": "string", "description": "Destination file path", "required": true, "default": null, "enum_values": null}], "returns": "Copy operation result", "examples": ["copy_file('/source/file.txt', '/backup/file.txt')"]}, {"name": "move_file", "description": "Move a file from source to destination", "category": "file_operations", "parameters": [{"name": "source", "type": "string", "description": "Source file path", "required": true, "default": null, "enum_values": null}, {"name": "destination", "type": "string", "description": "Destination file path", "required": true, "default": null, "enum_values": null}], "returns": "Move operation result", "examples": ["move_file('/temp/file.txt', '/archive/file.txt')"]}, {"name": "delete_file", "description": "Delete a file", "category": "file_operations", "parameters": [{"name": "file_path", "type": "string", "description": "Path to the file to delete", "required": true, "default": null, "enum_values": null}], "returns": "Delete operation result", "examples": ["delete_file('/temp/old_file.txt')"]}, {"name": "list_files", "description": "List files in a directory", "category": "file_operations", "parameters": [{"name": "directory", "type": "string", "description": "Directory path", "required": true, "default": null, "enum_values": null}, {"name": "pattern", "type": "string", "description": "File pattern", "required": false, "default": "*", "enum_values": null}, {"name": "recursive", "type": "boolean", "description": "Search recursively", "required": false, "default": false, "enum_values": null}], "returns": "List of file paths", "examples": ["list_files('/documents', '*.pdf', True)"]}, {"name": "get_file_info", "description": "Get information about a file", "category": "file_operations", "parameters": [{"name": "file_path", "type": "string", "description": "Path to the file", "required": true, "default": null, "enum_values": null}], "returns": "File information dictionary", "examples": ["get_file_info('/path/to/file.txt')"]}, {"name": "compress_files", "description": "Compress files into an archive", "category": "file_operations", "parameters": [{"name": "files", "type": "list", "description": "List of file paths to compress", "required": true, "default": null, "enum_values": null}, {"name": "archive_name", "type": "string", "description": "Name of the archive", "required": true, "default": null, "enum_values": null}, {"name": "format", "type": "string", "description": "Archive format", "required": false, "default": "zip", "enum_values": ["zip", "tar", "gz"]}], "returns": "Compression result", "examples": ["compress_files(['/file1.txt', '/file2.txt'], 'backup.zip')"]}, {"name": "extract_archive", "description": "Extract files from an archive", "category": "file_operations", "parameters": [{"name": "archive_path", "type": "string", "description": "Path to the archive", "required": true, "default": null, "enum_values": null}, {"name": "destination", "type": "string", "description": "Extraction destination", "required": true, "default": null, "enum_values": null}], "returns": "Extraction result", "examples": ["extract_archive('/backup.zip', '/extracted/')"]}], "web_operations": [{"name": "make_http_request", "description": "Make an HTTP request", "category": "web_operations", "parameters": [{"name": "url", "type": "string", "description": "Target URL", "required": true, "default": null, "enum_values": null}, {"name": "method", "type": "string", "description": "HTTP method", "required": false, "default": "GET", "enum_values": null}], "returns": "HTTP response", "examples": ["make_http_request('https://api.example.com/data')"]}, {"name": "scrape_webpage", "description": "Scrape content from webpage", "category": "web_operations", "parameters": [{"name": "url", "type": "string", "description": "Webpage URL", "required": true, "default": null, "enum_values": null}], "returns": "Scraped content", "examples": ["scrape_webpage('https://example.com')"]}, {"name": "download_file", "description": "Download file from URL", "category": "web_operations", "parameters": [{"name": "url", "type": "string", "description": "File URL", "required": true, "default": null, "enum_values": null}, {"name": "destination", "type": "string", "description": "Save location", "required": true, "default": null, "enum_values": null}], "returns": "Download result", "examples": ["download_file('https://example.com/file.pdf', '/downloads/')"]}, {"name": "search_web", "description": "Search the web", "category": "web_operations", "parameters": [{"name": "query", "type": "string", "description": "Search query", "required": true, "default": null, "enum_values": null}, {"name": "num_results", "type": "integer", "description": "Number of results", "required": false, "default": 10, "enum_values": null}], "returns": "Search results", "examples": ["search_web('AI function calling', 5)"]}], "analytics": [{"name": "calculate_statistics", "description": "Calculate statistical metrics", "category": "analytics", "parameters": [{"name": "data", "type": "list", "description": "Numerical data", "required": true, "default": null, "enum_values": null}, {"name": "metrics", "type": "list", "description": "Metrics to calculate", "required": false, "default": null, "enum_values": null}], "returns": "Statistical metrics", "examples": ["calculate_statistics([1, 2, 3, 4, 5])"]}, {"name": "create_chart", "description": "Create a chart from data", "category": "analytics", "parameters": [{"name": "data", "type": "list", "description": "Chart data", "required": true, "default": null, "enum_values": null}, {"name": "chart_type", "type": "string", "description": "Chart type", "required": true, "default": null, "enum_values": ["bar", "line", "pie", "scatter"]}, {"name": "x_field", "type": "string", "description": "X-axis field", "required": true, "default": null, "enum_values": null}, {"name": "y_field", "type": "string", "description": "Y-axis field", "required": true, "default": null, "enum_values": null}], "returns": "Chart creation result", "examples": ["create_chart(sales_data, 'bar', 'month', 'revenue')"]}, {"name": "generate_report", "description": "Generate formatted report", "category": "analytics", "parameters": [{"name": "data", "type": "list", "description": "Report data", "required": true, "default": null, "enum_values": null}, {"name": "template", "type": "string", "description": "Report template", "required": true, "default": null, "enum_values": null}, {"name": "title", "type": "string", "description": "Report title", "required": true, "default": null, "enum_values": null}], "returns": "Report generation result", "examples": ["generate_report(monthly_data, 'summary', 'Monthly Sales Report')"]}, {"name": "analyze_trends", "description": "Analyze trends in time-series data", "category": "analytics", "parameters": [{"name": "data", "type": "list", "description": "Time-series data", "required": true, "default": null, "enum_values": null}, {"name": "time_field", "type": "string", "description": "Time field name", "required": true, "default": null, "enum_values": null}, {"name": "value_field", "type": "string", "description": "Value field name", "required": true, "default": null, "enum_values": null}], "returns": "Trend analysis results", "examples": ["analyze_trends(sales_data, 'date', 'amount')"]}, {"name": "create_dashboard", "description": "Create dashboard with widgets", "category": "analytics", "parameters": [{"name": "widgets", "type": "list", "description": "Dashboard widgets", "required": true, "default": null, "enum_values": null}, {"name": "title", "type": "string", "description": "Dashboard title", "required": true, "default": null, "enum_values": null}], "returns": "Dashboard creation result", "examples": ["create_dashboard([chart1, chart2], 'Sales Dashboard')"]}, {"name": "export_data", "description": "Export data to various formats", "category": "analytics", "parameters": [{"name": "data", "type": "list", "description": "Data to export", "required": true, "default": null, "enum_values": null}, {"name": "format", "type": "string", "description": "Export format", "required": true, "default": null, "enum_values": ["csv", "json", "xlsx", "pdf"]}, {"name": "filename", "type": "string", "description": "Output filename", "required": true, "default": null, "enum_values": null}], "returns": "Export result", "examples": ["export_data(report_data, 'csv', 'monthly_report.csv')"]}], "database": [{"name": "execute_query", "description": "Execute database query", "category": "database", "parameters": [{"name": "query", "type": "string", "description": "SQL query", "required": true, "default": null, "enum_values": null}, {"name": "parameters", "type": "dict", "description": "Query parameters", "required": false, "default": null, "enum_values": null}], "returns": "Query results", "examples": ["execute_query('SELECT * FROM users WHERE active = ?', {'active': True})"]}, {"name": "insert_record", "description": "Insert record into table", "category": "database", "parameters": [{"name": "table", "type": "string", "description": "Table name", "required": true, "default": null, "enum_values": null}, {"name": "data", "type": "dict", "description": "Record data", "required": true, "default": null, "enum_values": null}], "returns": "Insert result", "examples": ["insert_record('users', {'name': '<PERSON>', 'email': '<EMAIL>'})"]}, {"name": "update_record", "description": "Update record in table", "category": "database", "parameters": [{"name": "table", "type": "string", "description": "Table name", "required": true, "default": null, "enum_values": null}, {"name": "record_id", "type": "any", "description": "Record ID", "required": true, "default": null, "enum_values": null}, {"name": "data", "type": "dict", "description": "Updated data", "required": true, "default": null, "enum_values": null}], "returns": "Update result", "examples": ["update_record('users', 123, {'email': '<EMAIL>'})"]}, {"name": "delete_record", "description": "Delete record from table", "category": "database", "parameters": [{"name": "table", "type": "string", "description": "Table name", "required": true, "default": null, "enum_values": null}, {"name": "record_id", "type": "any", "description": "Record ID", "required": true, "default": null, "enum_values": null}], "returns": "Delete result", "examples": ["delete_record('users', 123)"]}, {"name": "backup_database", "description": "Create database backup", "category": "database", "parameters": [{"name": "database_name", "type": "string", "description": "Database name", "required": true, "default": null, "enum_values": null}, {"name": "backup_location", "type": "string", "description": "Backup location", "required": true, "default": null, "enum_values": null}], "returns": "Backup result", "examples": ["backup_database('production_db', '/backups/daily/')"]}, {"name": "restore_database", "description": "Restore database from backup", "category": "database", "parameters": [{"name": "database_name", "type": "string", "description": "Database name", "required": true, "default": null, "enum_values": null}, {"name": "backup_file", "type": "string", "description": "Backup file path", "required": true, "default": null, "enum_values": null}], "returns": "Restore result", "examples": ["restore_database('production_db', '/backups/db_backup_20240315.sql')"]}], "scheduling": [{"name": "create_calendar_event", "description": "Create calendar event", "category": "scheduling", "parameters": [{"name": "title", "type": "string", "description": "Event title", "required": true, "default": null, "enum_values": null}, {"name": "start_time", "type": "string", "description": "Start time (ISO format)", "required": true, "default": null, "enum_values": null}, {"name": "end_time", "type": "string", "description": "End time (ISO format)", "required": true, "default": null, "enum_values": null}, {"name": "description", "type": "string", "description": "Event description", "required": false, "default": null, "enum_values": null}, {"name": "attendees", "type": "list", "description": "Attendee emails", "required": false, "default": null, "enum_values": null}], "returns": "Event creation result", "examples": ["create_calendar_event('Team Meeting', '2024-03-15T09:00:00', '2024-03-15T10:00:00')"]}, {"name": "schedule_task", "description": "Schedule a task", "category": "scheduling", "parameters": [{"name": "task_name", "type": "string", "description": "Task name", "required": true, "default": null, "enum_values": null}, {"name": "scheduled_time", "type": "string", "description": "Scheduled time (ISO format)", "required": true, "default": null, "enum_values": null}, {"name": "priority", "type": "string", "description": "Task priority", "required": false, "default": "normal", "enum_values": ["low", "normal", "high", "urgent"]}], "returns": "Task scheduling result", "examples": ["schedule_task('Review documents', '2024-03-15T14:00:00', 'high')"]}, {"name": "create_reminder", "description": "Create a reminder", "category": "scheduling", "parameters": [{"name": "message", "type": "string", "description": "Reminder message", "required": true, "default": null, "enum_values": null}, {"name": "remind_at", "type": "string", "description": "Reminder time (ISO format)", "required": true, "default": null, "enum_values": null}, {"name": "repeat", "type": "string", "description": "Repeat pattern", "required": false, "default": "none", "enum_values": ["none", "daily", "weekly", "monthly"]}], "returns": "Reminder creation result", "examples": ["create_reminder('Call dentist', '2024-03-15T16:30:00', 'none')"]}, {"name": "get_schedule", "description": "Get schedule for date", "category": "scheduling", "parameters": [{"name": "date", "type": "string", "description": "Date (YYYY-MM-DD)", "required": true, "default": null, "enum_values": null}, {"name": "user_id", "type": "string", "description": "User ID", "required": false, "default": null, "enum_values": null}], "returns": "Schedule items for the date", "examples": ["get_schedule('2024-03-15')"]}, {"name": "cancel_event", "description": "Cancel scheduled event", "category": "scheduling", "parameters": [{"name": "event_id", "type": "string", "description": "Event ID to cancel", "required": true, "default": null, "enum_values": null}], "returns": "Cancellation result", "examples": ["cancel_event('event_20240315_090000')"]}], "financial": [{"name": "retrieve_invoices", "description": "Retrieve invoices with filtering", "category": "financial", "parameters": [{"name": "month", "type": "string", "description": "Filter by month", "required": false, "default": null, "enum_values": null}, {"name": "year", "type": "integer", "description": "Filter by year", "required": false, "default": null, "enum_values": null}, {"name": "status", "type": "string", "description": "Filter by status", "required": false, "default": null, "enum_values": ["draft", "sent", "paid", "overdue", "cancelled"]}], "returns": "List of invoices", "examples": ["retrieve_invoices('March', 2024, 'paid')"]}, {"name": "calculate_total_amount", "description": "Calculate total amount from invoices", "category": "financial", "parameters": [{"name": "invoices", "type": "list", "description": "Invoice data", "required": true, "default": null, "enum_values": null}, {"name": "field", "type": "string", "description": "Field to sum", "required": false, "default": "amount", "enum_values": null}], "returns": "Total amount", "examples": ["calculate_total_amount(invoices, 'amount')"]}, {"name": "generate_invoice", "description": "Generate new invoice", "category": "financial", "parameters": [{"name": "customer", "type": "string", "description": "Customer name", "required": true, "default": null, "enum_values": null}, {"name": "items", "type": "list", "description": "Invoice items", "required": true, "default": null, "enum_values": null}, {"name": "due_date", "type": "string", "description": "Due date (YYYY-MM-DD)", "required": true, "default": null, "enum_values": null}], "returns": "Invoice generation result", "examples": ["generate_invoice('ACME Corp', [{'item': 'Service', 'amount': 1000}], '2024-04-15')"]}, {"name": "process_payment", "description": "Process payment for invoice", "category": "financial", "parameters": [{"name": "invoice_id", "type": "string", "description": "Invoice ID", "required": true, "default": null, "enum_values": null}, {"name": "amount", "type": "float", "description": "Payment amount", "required": true, "default": null, "enum_values": null}, {"name": "payment_method", "type": "string", "description": "Payment method", "required": true, "default": null, "enum_values": ["credit_card", "bank_transfer", "check", "cash"]}], "returns": "Payment processing result", "examples": ["process_payment('INV-001', 1500.00, 'credit_card')"]}, {"name": "generate_financial_report", "description": "Generate financial reports", "category": "financial", "parameters": [{"name": "period", "type": "string", "description": "Report period", "required": true, "default": null, "enum_values": null}, {"name": "report_type", "type": "string", "description": "Report type", "required": true, "default": null, "enum_values": ["income", "expense", "profit_loss", "balance_sheet", "cash_flow"]}], "returns": "Financial report", "examples": ["generate_financial_report('Q1 2024', 'profit_loss')"]}, {"name": "calculate_tax", "description": "Calculate tax on amount", "category": "financial", "parameters": [{"name": "amount", "type": "float", "description": "Base amount", "required": true, "default": null, "enum_values": null}, {"name": "tax_rate", "type": "float", "description": "Tax rate percentage", "required": true, "default": null, "enum_values": null}, {"name": "tax_type", "type": "string", "description": "Tax type", "required": false, "default": "sales", "enum_values": ["sales", "income", "property", "vat"]}], "returns": "Tax calculation result", "examples": ["calculate_tax(1000.00, 8.5, 'sales')"]}, {"name": "track_expenses", "description": "Track business expenses", "category": "financial", "parameters": [{"name": "category", "type": "string", "description": "Expense category", "required": true, "default": null, "enum_values": null}, {"name": "amount", "type": "float", "description": "Expense amount", "required": true, "default": null, "enum_values": null}, {"name": "description", "type": "string", "description": "Expense description", "required": true, "default": null, "enum_values": null}, {"name": "date", "type": "string", "description": "Expense date", "required": false, "default": null, "enum_values": null}], "returns": "Expense tracking result", "examples": ["track_expenses('Office Supplies', 150.00, 'Printer paper and ink')"]}], "system_operations": [{"name": "get_system_status", "description": "Get current system status", "category": "system_operations", "parameters": [], "returns": "System status metrics", "examples": ["get_system_status()"]}, {"name": "create_log_entry", "description": "Create a log entry", "category": "system_operations", "parameters": [{"name": "level", "type": "string", "description": "Log level", "required": true, "default": null, "enum_values": ["debug", "info", "warning", "error", "critical"]}, {"name": "message", "type": "string", "description": "Log message", "required": true, "default": null, "enum_values": null}, {"name": "component", "type": "string", "description": "Component name", "required": false, "default": "system", "enum_values": null}], "returns": "Log entry result", "examples": ["create_log_entry('info', 'System backup completed', 'backup_service')"]}, {"name": "monitor_service", "description": "Monitor a system service", "category": "system_operations", "parameters": [{"name": "service_name", "type": "string", "description": "Service name to monitor", "required": true, "default": null, "enum_values": null}], "returns": "Service monitoring data", "examples": ["monitor_service('nginx')"]}, {"name": "create_backup", "description": "Create system backup", "category": "system_operations", "parameters": [{"name": "source", "type": "string", "description": "Source path", "required": true, "default": null, "enum_values": null}, {"name": "destination", "type": "string", "description": "Backup destination", "required": true, "default": null, "enum_values": null}, {"name": "backup_type", "type": "string", "description": "Backup type", "required": false, "default": "full", "enum_values": ["full", "incremental", "differential"]}], "returns": "Backup creation result", "examples": ["create_backup('/var/www', '/backups/web', 'full')"]}, {"name": "restart_service", "description": "Restart a system service", "category": "system_operations", "parameters": [{"name": "service_name", "type": "string", "description": "Service name to restart", "required": true, "default": null, "enum_values": null}], "returns": "Service restart result", "examples": ["restart_service('apache2')"]}, {"name": "cleanup_temp_files", "description": "Clean up temporary files", "category": "system_operations", "parameters": [{"name": "directory", "type": "string", "description": "Directory to clean", "required": false, "default": "/tmp", "enum_values": null}, {"name": "older_than_days", "type": "integer", "description": "Delete files older than days", "required": false, "default": 7, "enum_values": null}], "returns": "Cleanup result", "examples": ["cleanup_temp_files('/tmp', 7)"]}, {"name": "check_disk_space", "description": "Check disk space usage", "category": "system_operations", "parameters": [{"name": "path", "type": "string", "description": "Path to check", "required": false, "default": "/", "enum_values": null}], "returns": "Disk space information", "examples": ["check_disk_space('/var')"]}]}, "all_functions": [{"name": "retrieve_data", "description": "Retrieve data from a specified source with optional filtering", "category": "data_operations", "parameters": [{"name": "source", "type": "string", "description": "Data source identifier", "required": true, "default": null, "enum_values": null}, {"name": "filters", "type": "dict", "description": "Filter conditions", "required": false, "default": null, "enum_values": null}, {"name": "limit", "type": "integer", "description": "Maximum number of records", "required": false, "default": null, "enum_values": null}], "returns": "List of data records", "examples": ["retrieve_data('invoices', {'month': 'March'}, 100)"]}, {"name": "filter_data", "description": "Filter data based on specified conditions", "category": "data_operations", "parameters": [{"name": "data", "type": "list", "description": "Data to filter", "required": true, "default": null, "enum_values": null}, {"name": "conditions", "type": "dict", "description": "Filter conditions", "required": true, "default": null, "enum_values": null}], "returns": "Filtered data list", "examples": ["filter_data(data, {'status': 'active'})"]}, {"name": "sort_data", "description": "Sort data by a specified field", "category": "data_operations", "parameters": [{"name": "data", "type": "list", "description": "Data to sort", "required": true, "default": null, "enum_values": null}, {"name": "sort_by", "type": "string", "description": "Field to sort by", "required": true, "default": null, "enum_values": null}, {"name": "ascending", "type": "boolean", "description": "Sort order", "required": false, "default": true, "enum_values": null}], "returns": "Sorted data list", "examples": ["sort_data(data, 'date', False)"]}, {"name": "group_data", "description": "Group data by a specified field", "category": "data_operations", "parameters": [{"name": "data", "type": "list", "description": "Data to group", "required": true, "default": null, "enum_values": null}, {"name": "group_by", "type": "string", "description": "Field to group by", "required": true, "default": null, "enum_values": null}], "returns": "Dictionary of grouped data", "examples": ["group_data(data, 'category')"]}, {"name": "aggregate_data", "description": "Aggregate data using specified operation", "category": "data_operations", "parameters": [{"name": "data", "type": "list", "description": "Data to aggregate", "required": true, "default": null, "enum_values": null}, {"name": "field", "type": "string", "description": "Field to aggregate", "required": true, "default": null, "enum_values": null}, {"name": "operation", "type": "string", "description": "Aggregation operation", "required": true, "default": null, "enum_values": ["sum", "avg", "count", "min", "max"]}], "returns": "Aggregated value", "examples": ["aggregate_data(invoices, 'amount', 'sum')"]}, {"name": "transform_data", "description": "Transform data fields based on specified transformations", "category": "data_operations", "parameters": [{"name": "data", "type": "list", "description": "Data to transform", "required": true, "default": null, "enum_values": null}, {"name": "transformations", "type": "dict", "description": "Field transformations", "required": true, "default": null, "enum_values": null}], "returns": "Transformed data list", "examples": ["transform_data(data, {'name': 'uppercase', 'amount': 'round'})"]}, {"name": "merge_data", "description": "Merge two datasets on a common key", "category": "data_operations", "parameters": [{"name": "data1", "type": "list", "description": "First dataset", "required": true, "default": null, "enum_values": null}, {"name": "data2", "type": "list", "description": "Second dataset", "required": true, "default": null, "enum_values": null}, {"name": "join_key", "type": "string", "description": "Common key for joining", "required": true, "default": null, "enum_values": null}], "returns": "Merged data list", "examples": ["merge_data(customers, orders, 'customer_id')"]}, {"name": "deduplicate_data", "description": "Remove duplicate entries from data", "category": "data_operations", "parameters": [{"name": "data", "type": "list", "description": "Data to deduplicate", "required": true, "default": null, "enum_values": null}, {"name": "key", "type": "string", "description": "Key field for deduplication", "required": false, "default": null, "enum_values": null}], "returns": "Deduplicated data list", "examples": ["deduplicate_data(data, 'id')"]}, {"name": "send_email", "description": "Send an email to specified recipients", "category": "communication", "parameters": [{"name": "to", "type": "string", "description": "Recipient email address", "required": true, "default": null, "enum_values": null}, {"name": "subject", "type": "string", "description": "Email subject", "required": true, "default": null, "enum_values": null}, {"name": "body", "type": "string", "description": "Email body content", "required": true, "default": null, "enum_values": null}, {"name": "cc", "type": "list", "description": "CC recipients", "required": false, "default": null, "enum_values": null}, {"name": "attachments", "type": "list", "description": "File attachments", "required": false, "default": null, "enum_values": null}], "returns": "Email sending result with message ID and status", "examples": ["send_email('<EMAIL>', 'Monthly Report', 'Please find the report attached.')"]}, {"name": "send_sms", "description": "Send SMS to a phone number", "category": "communication", "parameters": [{"name": "to", "type": "string", "description": "Recipient phone number", "required": true, "default": null, "enum_values": null}, {"name": "message", "type": "string", "description": "SMS message content", "required": true, "default": null, "enum_values": null}], "returns": "SMS sending result with message ID and status", "examples": ["send_sms('+1234567890', 'Your order has been shipped!')"]}, {"name": "send_notification", "description": "Send a notification through specified channels", "category": "communication", "parameters": [{"name": "title", "type": "string", "description": "Notification title", "required": true, "default": null, "enum_values": null}, {"name": "message", "type": "string", "description": "Notification message", "required": true, "default": null, "enum_values": null}, {"name": "priority", "type": "string", "description": "Notification priority", "required": false, "default": "normal", "enum_values": ["low", "normal", "high", "urgent"]}, {"name": "channels", "type": "list", "description": "Notification channels", "required": false, "default": null, "enum_values": null}], "returns": "Notification sending result", "examples": ["send_notification('System Alert', 'Database backup completed', 'normal', ['slack', 'email'])"]}, {"name": "create_message_template", "description": "Create a reusable message template", "category": "communication", "parameters": [{"name": "name", "type": "string", "description": "Template name", "required": true, "default": null, "enum_values": null}, {"name": "template", "type": "string", "description": "Message template with variables", "required": true, "default": null, "enum_values": null}, {"name": "variables", "type": "list", "description": "List of template variables", "required": true, "default": null, "enum_values": null}], "returns": "Created template information", "examples": ["create_message_template('welcome', 'Hello {name}, welcome to {company}!', ['name', 'company'])"]}, {"name": "send_templated_message", "description": "Send a message using a predefined template", "category": "communication", "parameters": [{"name": "template_id", "type": "string", "description": "Template identifier", "required": true, "default": null, "enum_values": null}, {"name": "recipient", "type": "string", "description": "Message recipient", "required": true, "default": null, "enum_values": null}, {"name": "variables", "type": "dict", "description": "Template variable values", "required": true, "default": null, "enum_values": null}, {"name": "channel", "type": "string", "description": "Communication channel", "required": false, "default": "email", "enum_values": null}], "returns": "Message sending result", "examples": ["send_templated_message('welcome_template', '<EMAIL>', {'name': '<PERSON>', 'company': 'ACME'})"]}, {"name": "send_bulk_messages", "description": "Send the same message to multiple recipients", "category": "communication", "parameters": [{"name": "recipients", "type": "list", "description": "List of recipients", "required": true, "default": null, "enum_values": null}, {"name": "message", "type": "string", "description": "Message content", "required": true, "default": null, "enum_values": null}, {"name": "channel", "type": "string", "description": "Communication channel", "required": false, "default": "email", "enum_values": null}], "returns": "Bulk sending result with statistics", "examples": ["send_bulk_messages(['<EMAIL>', '<EMAIL>'], 'Newsletter content')"]}, {"name": "schedule_message", "description": "Schedule a message to be sent at a specific time", "category": "communication", "parameters": [{"name": "recipient", "type": "string", "description": "Message recipient", "required": true, "default": null, "enum_values": null}, {"name": "message", "type": "string", "description": "Message content", "required": true, "default": null, "enum_values": null}, {"name": "send_at", "type": "string", "description": "Scheduled send time (ISO format)", "required": true, "default": null, "enum_values": null}, {"name": "channel", "type": "string", "description": "Communication channel", "required": false, "default": "email", "enum_values": null}], "returns": "Scheduled message information", "examples": ["schedule_message('<EMAIL>', 'Reminder: Meeting tomorrow', '2024-03-15T09:00:00')"]}, {"name": "get_message_status", "description": "Get the delivery status of a message", "category": "communication", "parameters": [{"name": "message_id", "type": "string", "description": "Message identifier", "required": true, "default": null, "enum_values": null}], "returns": "Message status information", "examples": ["get_message_status('email_20240315_143022')"]}, {"name": "read_file", "description": "Read content from a file", "category": "file_operations", "parameters": [{"name": "file_path", "type": "string", "description": "Path to the file", "required": true, "default": null, "enum_values": null}, {"name": "encoding", "type": "string", "description": "File encoding", "required": false, "default": "utf-8", "enum_values": null}], "returns": "File content as string", "examples": ["read_file('/path/to/document.txt')"]}, {"name": "write_file", "description": "Write content to a file", "category": "file_operations", "parameters": [{"name": "file_path", "type": "string", "description": "Path to the file", "required": true, "default": null, "enum_values": null}, {"name": "content", "type": "string", "description": "Content to write", "required": true, "default": null, "enum_values": null}, {"name": "encoding", "type": "string", "description": "File encoding", "required": false, "default": "utf-8", "enum_values": null}, {"name": "append", "type": "boolean", "description": "Append to file", "required": false, "default": false, "enum_values": null}], "returns": "Write operation result", "examples": ["write_file('/path/to/output.txt', 'Hello World')"]}, {"name": "copy_file", "description": "Copy a file from source to destination", "category": "file_operations", "parameters": [{"name": "source", "type": "string", "description": "Source file path", "required": true, "default": null, "enum_values": null}, {"name": "destination", "type": "string", "description": "Destination file path", "required": true, "default": null, "enum_values": null}], "returns": "Copy operation result", "examples": ["copy_file('/source/file.txt', '/backup/file.txt')"]}, {"name": "move_file", "description": "Move a file from source to destination", "category": "file_operations", "parameters": [{"name": "source", "type": "string", "description": "Source file path", "required": true, "default": null, "enum_values": null}, {"name": "destination", "type": "string", "description": "Destination file path", "required": true, "default": null, "enum_values": null}], "returns": "Move operation result", "examples": ["move_file('/temp/file.txt', '/archive/file.txt')"]}, {"name": "delete_file", "description": "Delete a file", "category": "file_operations", "parameters": [{"name": "file_path", "type": "string", "description": "Path to the file to delete", "required": true, "default": null, "enum_values": null}], "returns": "Delete operation result", "examples": ["delete_file('/temp/old_file.txt')"]}, {"name": "list_files", "description": "List files in a directory", "category": "file_operations", "parameters": [{"name": "directory", "type": "string", "description": "Directory path", "required": true, "default": null, "enum_values": null}, {"name": "pattern", "type": "string", "description": "File pattern", "required": false, "default": "*", "enum_values": null}, {"name": "recursive", "type": "boolean", "description": "Search recursively", "required": false, "default": false, "enum_values": null}], "returns": "List of file paths", "examples": ["list_files('/documents', '*.pdf', True)"]}, {"name": "get_file_info", "description": "Get information about a file", "category": "file_operations", "parameters": [{"name": "file_path", "type": "string", "description": "Path to the file", "required": true, "default": null, "enum_values": null}], "returns": "File information dictionary", "examples": ["get_file_info('/path/to/file.txt')"]}, {"name": "compress_files", "description": "Compress files into an archive", "category": "file_operations", "parameters": [{"name": "files", "type": "list", "description": "List of file paths to compress", "required": true, "default": null, "enum_values": null}, {"name": "archive_name", "type": "string", "description": "Name of the archive", "required": true, "default": null, "enum_values": null}, {"name": "format", "type": "string", "description": "Archive format", "required": false, "default": "zip", "enum_values": ["zip", "tar", "gz"]}], "returns": "Compression result", "examples": ["compress_files(['/file1.txt', '/file2.txt'], 'backup.zip')"]}, {"name": "extract_archive", "description": "Extract files from an archive", "category": "file_operations", "parameters": [{"name": "archive_path", "type": "string", "description": "Path to the archive", "required": true, "default": null, "enum_values": null}, {"name": "destination", "type": "string", "description": "Extraction destination", "required": true, "default": null, "enum_values": null}], "returns": "Extraction result", "examples": ["extract_archive('/backup.zip', '/extracted/')"]}, {"name": "make_http_request", "description": "Make an HTTP request", "category": "web_operations", "parameters": [{"name": "url", "type": "string", "description": "Target URL", "required": true, "default": null, "enum_values": null}, {"name": "method", "type": "string", "description": "HTTP method", "required": false, "default": "GET", "enum_values": null}], "returns": "HTTP response", "examples": ["make_http_request('https://api.example.com/data')"]}, {"name": "scrape_webpage", "description": "Scrape content from webpage", "category": "web_operations", "parameters": [{"name": "url", "type": "string", "description": "Webpage URL", "required": true, "default": null, "enum_values": null}], "returns": "Scraped content", "examples": ["scrape_webpage('https://example.com')"]}, {"name": "download_file", "description": "Download file from URL", "category": "web_operations", "parameters": [{"name": "url", "type": "string", "description": "File URL", "required": true, "default": null, "enum_values": null}, {"name": "destination", "type": "string", "description": "Save location", "required": true, "default": null, "enum_values": null}], "returns": "Download result", "examples": ["download_file('https://example.com/file.pdf', '/downloads/')"]}, {"name": "search_web", "description": "Search the web", "category": "web_operations", "parameters": [{"name": "query", "type": "string", "description": "Search query", "required": true, "default": null, "enum_values": null}, {"name": "num_results", "type": "integer", "description": "Number of results", "required": false, "default": 10, "enum_values": null}], "returns": "Search results", "examples": ["search_web('AI function calling', 5)"]}, {"name": "calculate_statistics", "description": "Calculate statistical metrics", "category": "analytics", "parameters": [{"name": "data", "type": "list", "description": "Numerical data", "required": true, "default": null, "enum_values": null}, {"name": "metrics", "type": "list", "description": "Metrics to calculate", "required": false, "default": null, "enum_values": null}], "returns": "Statistical metrics", "examples": ["calculate_statistics([1, 2, 3, 4, 5])"]}, {"name": "create_chart", "description": "Create a chart from data", "category": "analytics", "parameters": [{"name": "data", "type": "list", "description": "Chart data", "required": true, "default": null, "enum_values": null}, {"name": "chart_type", "type": "string", "description": "Chart type", "required": true, "default": null, "enum_values": ["bar", "line", "pie", "scatter"]}, {"name": "x_field", "type": "string", "description": "X-axis field", "required": true, "default": null, "enum_values": null}, {"name": "y_field", "type": "string", "description": "Y-axis field", "required": true, "default": null, "enum_values": null}], "returns": "Chart creation result", "examples": ["create_chart(sales_data, 'bar', 'month', 'revenue')"]}, {"name": "generate_report", "description": "Generate formatted report", "category": "analytics", "parameters": [{"name": "data", "type": "list", "description": "Report data", "required": true, "default": null, "enum_values": null}, {"name": "template", "type": "string", "description": "Report template", "required": true, "default": null, "enum_values": null}, {"name": "title", "type": "string", "description": "Report title", "required": true, "default": null, "enum_values": null}], "returns": "Report generation result", "examples": ["generate_report(monthly_data, 'summary', 'Monthly Sales Report')"]}, {"name": "analyze_trends", "description": "Analyze trends in time-series data", "category": "analytics", "parameters": [{"name": "data", "type": "list", "description": "Time-series data", "required": true, "default": null, "enum_values": null}, {"name": "time_field", "type": "string", "description": "Time field name", "required": true, "default": null, "enum_values": null}, {"name": "value_field", "type": "string", "description": "Value field name", "required": true, "default": null, "enum_values": null}], "returns": "Trend analysis results", "examples": ["analyze_trends(sales_data, 'date', 'amount')"]}, {"name": "create_dashboard", "description": "Create dashboard with widgets", "category": "analytics", "parameters": [{"name": "widgets", "type": "list", "description": "Dashboard widgets", "required": true, "default": null, "enum_values": null}, {"name": "title", "type": "string", "description": "Dashboard title", "required": true, "default": null, "enum_values": null}], "returns": "Dashboard creation result", "examples": ["create_dashboard([chart1, chart2], 'Sales Dashboard')"]}, {"name": "export_data", "description": "Export data to various formats", "category": "analytics", "parameters": [{"name": "data", "type": "list", "description": "Data to export", "required": true, "default": null, "enum_values": null}, {"name": "format", "type": "string", "description": "Export format", "required": true, "default": null, "enum_values": ["csv", "json", "xlsx", "pdf"]}, {"name": "filename", "type": "string", "description": "Output filename", "required": true, "default": null, "enum_values": null}], "returns": "Export result", "examples": ["export_data(report_data, 'csv', 'monthly_report.csv')"]}, {"name": "execute_query", "description": "Execute database query", "category": "database", "parameters": [{"name": "query", "type": "string", "description": "SQL query", "required": true, "default": null, "enum_values": null}, {"name": "parameters", "type": "dict", "description": "Query parameters", "required": false, "default": null, "enum_values": null}], "returns": "Query results", "examples": ["execute_query('SELECT * FROM users WHERE active = ?', {'active': True})"]}, {"name": "insert_record", "description": "Insert record into table", "category": "database", "parameters": [{"name": "table", "type": "string", "description": "Table name", "required": true, "default": null, "enum_values": null}, {"name": "data", "type": "dict", "description": "Record data", "required": true, "default": null, "enum_values": null}], "returns": "Insert result", "examples": ["insert_record('users', {'name': '<PERSON>', 'email': '<EMAIL>'})"]}, {"name": "update_record", "description": "Update record in table", "category": "database", "parameters": [{"name": "table", "type": "string", "description": "Table name", "required": true, "default": null, "enum_values": null}, {"name": "record_id", "type": "any", "description": "Record ID", "required": true, "default": null, "enum_values": null}, {"name": "data", "type": "dict", "description": "Updated data", "required": true, "default": null, "enum_values": null}], "returns": "Update result", "examples": ["update_record('users', 123, {'email': '<EMAIL>'})"]}, {"name": "delete_record", "description": "Delete record from table", "category": "database", "parameters": [{"name": "table", "type": "string", "description": "Table name", "required": true, "default": null, "enum_values": null}, {"name": "record_id", "type": "any", "description": "Record ID", "required": true, "default": null, "enum_values": null}], "returns": "Delete result", "examples": ["delete_record('users', 123)"]}, {"name": "backup_database", "description": "Create database backup", "category": "database", "parameters": [{"name": "database_name", "type": "string", "description": "Database name", "required": true, "default": null, "enum_values": null}, {"name": "backup_location", "type": "string", "description": "Backup location", "required": true, "default": null, "enum_values": null}], "returns": "Backup result", "examples": ["backup_database('production_db', '/backups/daily/')"]}, {"name": "restore_database", "description": "Restore database from backup", "category": "database", "parameters": [{"name": "database_name", "type": "string", "description": "Database name", "required": true, "default": null, "enum_values": null}, {"name": "backup_file", "type": "string", "description": "Backup file path", "required": true, "default": null, "enum_values": null}], "returns": "Restore result", "examples": ["restore_database('production_db', '/backups/db_backup_20240315.sql')"]}, {"name": "create_calendar_event", "description": "Create calendar event", "category": "scheduling", "parameters": [{"name": "title", "type": "string", "description": "Event title", "required": true, "default": null, "enum_values": null}, {"name": "start_time", "type": "string", "description": "Start time (ISO format)", "required": true, "default": null, "enum_values": null}, {"name": "end_time", "type": "string", "description": "End time (ISO format)", "required": true, "default": null, "enum_values": null}, {"name": "description", "type": "string", "description": "Event description", "required": false, "default": null, "enum_values": null}, {"name": "attendees", "type": "list", "description": "Attendee emails", "required": false, "default": null, "enum_values": null}], "returns": "Event creation result", "examples": ["create_calendar_event('Team Meeting', '2024-03-15T09:00:00', '2024-03-15T10:00:00')"]}, {"name": "schedule_task", "description": "Schedule a task", "category": "scheduling", "parameters": [{"name": "task_name", "type": "string", "description": "Task name", "required": true, "default": null, "enum_values": null}, {"name": "scheduled_time", "type": "string", "description": "Scheduled time (ISO format)", "required": true, "default": null, "enum_values": null}, {"name": "priority", "type": "string", "description": "Task priority", "required": false, "default": "normal", "enum_values": ["low", "normal", "high", "urgent"]}], "returns": "Task scheduling result", "examples": ["schedule_task('Review documents', '2024-03-15T14:00:00', 'high')"]}, {"name": "create_reminder", "description": "Create a reminder", "category": "scheduling", "parameters": [{"name": "message", "type": "string", "description": "Reminder message", "required": true, "default": null, "enum_values": null}, {"name": "remind_at", "type": "string", "description": "Reminder time (ISO format)", "required": true, "default": null, "enum_values": null}, {"name": "repeat", "type": "string", "description": "Repeat pattern", "required": false, "default": "none", "enum_values": ["none", "daily", "weekly", "monthly"]}], "returns": "Reminder creation result", "examples": ["create_reminder('Call dentist', '2024-03-15T16:30:00', 'none')"]}, {"name": "get_schedule", "description": "Get schedule for date", "category": "scheduling", "parameters": [{"name": "date", "type": "string", "description": "Date (YYYY-MM-DD)", "required": true, "default": null, "enum_values": null}, {"name": "user_id", "type": "string", "description": "User ID", "required": false, "default": null, "enum_values": null}], "returns": "Schedule items for the date", "examples": ["get_schedule('2024-03-15')"]}, {"name": "cancel_event", "description": "Cancel scheduled event", "category": "scheduling", "parameters": [{"name": "event_id", "type": "string", "description": "Event ID to cancel", "required": true, "default": null, "enum_values": null}], "returns": "Cancellation result", "examples": ["cancel_event('event_20240315_090000')"]}, {"name": "retrieve_invoices", "description": "Retrieve invoices with filtering", "category": "financial", "parameters": [{"name": "month", "type": "string", "description": "Filter by month", "required": false, "default": null, "enum_values": null}, {"name": "year", "type": "integer", "description": "Filter by year", "required": false, "default": null, "enum_values": null}, {"name": "status", "type": "string", "description": "Filter by status", "required": false, "default": null, "enum_values": ["draft", "sent", "paid", "overdue", "cancelled"]}], "returns": "List of invoices", "examples": ["retrieve_invoices('March', 2024, 'paid')"]}, {"name": "calculate_total_amount", "description": "Calculate total amount from invoices", "category": "financial", "parameters": [{"name": "invoices", "type": "list", "description": "Invoice data", "required": true, "default": null, "enum_values": null}, {"name": "field", "type": "string", "description": "Field to sum", "required": false, "default": "amount", "enum_values": null}], "returns": "Total amount", "examples": ["calculate_total_amount(invoices, 'amount')"]}, {"name": "generate_invoice", "description": "Generate new invoice", "category": "financial", "parameters": [{"name": "customer", "type": "string", "description": "Customer name", "required": true, "default": null, "enum_values": null}, {"name": "items", "type": "list", "description": "Invoice items", "required": true, "default": null, "enum_values": null}, {"name": "due_date", "type": "string", "description": "Due date (YYYY-MM-DD)", "required": true, "default": null, "enum_values": null}], "returns": "Invoice generation result", "examples": ["generate_invoice('ACME Corp', [{'item': 'Service', 'amount': 1000}], '2024-04-15')"]}, {"name": "process_payment", "description": "Process payment for invoice", "category": "financial", "parameters": [{"name": "invoice_id", "type": "string", "description": "Invoice ID", "required": true, "default": null, "enum_values": null}, {"name": "amount", "type": "float", "description": "Payment amount", "required": true, "default": null, "enum_values": null}, {"name": "payment_method", "type": "string", "description": "Payment method", "required": true, "default": null, "enum_values": ["credit_card", "bank_transfer", "check", "cash"]}], "returns": "Payment processing result", "examples": ["process_payment('INV-001', 1500.00, 'credit_card')"]}, {"name": "generate_financial_report", "description": "Generate financial reports", "category": "financial", "parameters": [{"name": "period", "type": "string", "description": "Report period", "required": true, "default": null, "enum_values": null}, {"name": "report_type", "type": "string", "description": "Report type", "required": true, "default": null, "enum_values": ["income", "expense", "profit_loss", "balance_sheet", "cash_flow"]}], "returns": "Financial report", "examples": ["generate_financial_report('Q1 2024', 'profit_loss')"]}, {"name": "calculate_tax", "description": "Calculate tax on amount", "category": "financial", "parameters": [{"name": "amount", "type": "float", "description": "Base amount", "required": true, "default": null, "enum_values": null}, {"name": "tax_rate", "type": "float", "description": "Tax rate percentage", "required": true, "default": null, "enum_values": null}, {"name": "tax_type", "type": "string", "description": "Tax type", "required": false, "default": "sales", "enum_values": ["sales", "income", "property", "vat"]}], "returns": "Tax calculation result", "examples": ["calculate_tax(1000.00, 8.5, 'sales')"]}, {"name": "track_expenses", "description": "Track business expenses", "category": "financial", "parameters": [{"name": "category", "type": "string", "description": "Expense category", "required": true, "default": null, "enum_values": null}, {"name": "amount", "type": "float", "description": "Expense amount", "required": true, "default": null, "enum_values": null}, {"name": "description", "type": "string", "description": "Expense description", "required": true, "default": null, "enum_values": null}, {"name": "date", "type": "string", "description": "Expense date", "required": false, "default": null, "enum_values": null}], "returns": "Expense tracking result", "examples": ["track_expenses('Office Supplies', 150.00, 'Printer paper and ink')"]}, {"name": "get_system_status", "description": "Get current system status", "category": "system_operations", "parameters": [], "returns": "System status metrics", "examples": ["get_system_status()"]}, {"name": "create_log_entry", "description": "Create a log entry", "category": "system_operations", "parameters": [{"name": "level", "type": "string", "description": "Log level", "required": true, "default": null, "enum_values": ["debug", "info", "warning", "error", "critical"]}, {"name": "message", "type": "string", "description": "Log message", "required": true, "default": null, "enum_values": null}, {"name": "component", "type": "string", "description": "Component name", "required": false, "default": "system", "enum_values": null}], "returns": "Log entry result", "examples": ["create_log_entry('info', 'System backup completed', 'backup_service')"]}, {"name": "monitor_service", "description": "Monitor a system service", "category": "system_operations", "parameters": [{"name": "service_name", "type": "string", "description": "Service name to monitor", "required": true, "default": null, "enum_values": null}], "returns": "Service monitoring data", "examples": ["monitor_service('nginx')"]}, {"name": "create_backup", "description": "Create system backup", "category": "system_operations", "parameters": [{"name": "source", "type": "string", "description": "Source path", "required": true, "default": null, "enum_values": null}, {"name": "destination", "type": "string", "description": "Backup destination", "required": true, "default": null, "enum_values": null}, {"name": "backup_type", "type": "string", "description": "Backup type", "required": false, "default": "full", "enum_values": ["full", "incremental", "differential"]}], "returns": "Backup creation result", "examples": ["create_backup('/var/www', '/backups/web', 'full')"]}, {"name": "restart_service", "description": "Restart a system service", "category": "system_operations", "parameters": [{"name": "service_name", "type": "string", "description": "Service name to restart", "required": true, "default": null, "enum_values": null}], "returns": "Service restart result", "examples": ["restart_service('apache2')"]}, {"name": "cleanup_temp_files", "description": "Clean up temporary files", "category": "system_operations", "parameters": [{"name": "directory", "type": "string", "description": "Directory to clean", "required": false, "default": "/tmp", "enum_values": null}, {"name": "older_than_days", "type": "integer", "description": "Delete files older than days", "required": false, "default": 7, "enum_values": null}], "returns": "Cleanup result", "examples": ["cleanup_temp_files('/tmp', 7)"]}, {"name": "check_disk_space", "description": "Check disk space usage", "category": "system_operations", "parameters": [{"name": "path", "type": "string", "description": "Path to check", "required": false, "default": "/", "enum_values": null}], "returns": "Disk space information", "examples": ["check_disk_space('/var')"]}]}