import { APIResource } from "../../resource.js";
import * as SpeechAPI from "./speech.js";
import { Speech, SpeechCreateParams } from "./speech.js";
import * as TranscriptionsAPI from "./transcriptions.js";
import { Transcription, TranscriptionCreateParams, Transcriptions } from "./transcriptions.js";
import * as TranslationsAPI from "./translations.js";
import { Translation, TranslationCreateParams, Translations } from "./translations.js";
export declare class Audio extends APIResource {
    speech: SpeechAPI.Speech;
    transcriptions: TranscriptionsAPI.Transcriptions;
    translations: TranslationsAPI.Translations;
}
export declare namespace Audio {
    export { Speech as Speech, type SpeechCreateParams as SpeechCreateParams };
    export { Transcriptions as Transcriptions, type Transcription as Transcription, type TranscriptionCreateParams as TranscriptionCreateParams, };
    export { Translations as Translations, type Translation as Translation, type TranslationCreateParams as TranslationCreateParams, };
}
//# sourceMappingURL=audio.d.ts.map