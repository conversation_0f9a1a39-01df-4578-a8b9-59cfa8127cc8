                              
                              
                              
                              
import * as types from "../_shims/node-types.js";
declare module '../_shims/manual-types' {
    namespace manual {
        type Agent = types.Agent;
        export import fetch = types.fetch;
        type Request = types.Request;
        type RequestInfo = types.RequestInfo;
        type RequestInit = types.RequestInit;
        type Response = types.Response;
        type ResponseInit = types.ResponseInit;
        type ResponseType = types.ResponseType;
        type BodyInit = types.BodyInit;
        type Headers = types.Headers;
        type HeadersInit = types.HeadersInit;
        type BlobPropertyBag = types.BlobPropertyBag;
        type FilePropertyBag = types.FilePropertyBag;
        type FileFromPathOptions = types.FileFromPathOptions;
        export import FormData = types.FormData;
        export import File = types.File;
        export import Blob = types.Blob;
        type Readable = types.Readable;
        type FsReadStream = types.FsReadStream;
        export import ReadableStream = types.ReadableStream;
    }
}
//# sourceMappingURL=node.d.ts.map