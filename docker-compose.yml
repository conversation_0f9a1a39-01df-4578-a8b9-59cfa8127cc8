version: '3.8'

services:
  ai-pipeline:
    build: .
    ports:
      - "8000:8000"
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
      - ./logs:/app/logs
    environment:
      - OLLAMA_HOST=0.0.0.0
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  ollama_data:
