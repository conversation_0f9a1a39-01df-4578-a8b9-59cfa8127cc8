"""
Test suite for the AI Function Calling Pipeline.
"""

import pytest
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

from src.pipeline.core import FunctionCallingPipeline
from src.models.schemas import QueryRequest, ExecutionPlan, FunctionCall
from src.functions.registry import function_registry


class TestFunctionRegistry:
    """Test the function registry."""
    
    def test_registry_initialization(self):
        """Test that the registry loads functions correctly."""
        assert len(function_registry._functions) > 0
        assert len(function_registry._schemas) > 0
        assert len(function_registry.get_categories()) > 0
    
    def test_function_validation(self):
        """Test function call validation."""
        # Valid function call
        assert function_registry.validate_function_call(
            "retrieve_data", 
            {"source": "test", "limit": 10}
        )
        
        # Invalid function call (missing required parameter)
        assert not function_registry.validate_function_call(
            "retrieve_data", 
            {"limit": 10}  # missing 'source'
        )
    
    def test_function_search(self):
        """Test function search functionality."""
        results = function_registry.search_functions("email")
        assert len(results) > 0
        assert any("email" in func.name.lower() for func in results)


class TestPipeline:
    """Test the main pipeline functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.pipeline = FunctionCallingPipeline()
    
    def test_pipeline_initialization(self):
        """Test pipeline initialization."""
        assert self.pipeline is not None
        assert self.pipeline.executor is not None
        assert self.pipeline.ai_planner is not None
    
    def test_function_library_access(self):
        """Test function library access."""
        library = self.pipeline.get_function_library()
        assert "total_functions" in library
        assert "categories" in library
        assert library["total_functions"] > 0
    
    def test_health_status(self):
        """Test health status endpoint."""
        status = self.pipeline.get_health_status()
        assert "status" in status
        assert "functions_loaded" in status
        assert status["functions_loaded"] > 0
    
    def test_execution_plan_validation(self):
        """Test execution plan validation."""
        # Create a simple execution plan
        plan = ExecutionPlan(
            query="Test query",
            reasoning="Test reasoning",
            function_calls=[
                FunctionCall(
                    function_name="retrieve_data",
                    parameters={"source": "test"},
                    output_variable="data"
                )
            ]
        )
        
        validation = self.pipeline.executor.validate_execution_plan(plan)
        assert validation["valid"] is True
        assert len(validation["errors"]) == 0


class TestFunctions:
    """Test individual function implementations."""
    
    def test_data_operations(self):
        """Test data operation functions."""
        from src.functions.data_operations import retrieve_data, filter_data, aggregate_data
        
        # Test retrieve_data
        data = retrieve_data("test_source")
        assert isinstance(data, list)
        assert len(data) > 0
        
        # Test filter_data
        filtered = filter_data(data, {"category": "A"})
        assert isinstance(filtered, list)
        
        # Test aggregate_data
        total = aggregate_data(data, "value", "sum")
        assert isinstance(total, (int, float))
    
    def test_communication_functions(self):
        """Test communication functions."""
        from src.functions.communication import send_email, send_sms
        
        # Test send_email
        result = send_email("<EMAIL>", "Test Subject", "Test Body")
        assert result["success"] is True
        assert "message_id" in result
        
        # Test send_sms
        result = send_sms("+1234567890", "Test message")
        assert result["success"] is True
        assert "message_id" in result
    
    def test_financial_functions(self):
        """Test financial functions."""
        from src.functions.financial import retrieve_invoices, calculate_total_amount
        
        # Test retrieve_invoices
        invoices = retrieve_invoices("March", 2024)
        assert isinstance(invoices, list)
        
        # Test calculate_total_amount
        total = calculate_total_amount(invoices)
        assert isinstance(total, (int, float))
        assert total >= 0


def test_example_queries():
    """Test with example queries to ensure they can be processed."""
    pipeline = FunctionCallingPipeline()
    
    example_queries = [
        "Retrieve all invoices for March and calculate the total amount",
        "Send an <NAME_EMAIL> with subject 'Test'",
        "Create a backup of the database",
        "Get system status and create a log entry"
    ]
    
    for query in example_queries:
        request = QueryRequest(query=query)
        # Note: This test will only work if Ollama is running
        # In a real test environment, you might want to mock the AI client
        try:
            response = pipeline.process_query(request)
            # If Ollama is available, we should get a response
            if pipeline.is_ready():
                assert response is not None
        except Exception as e:
            # If Ollama is not available, that's expected in test environment
            print(f"Skipping AI test due to: {e}")


if __name__ == "__main__":
    pytest.main([__file__])
