// AI Function Calling Pipeline Frontend

class PipelineUI {
    constructor() {
        this.currentPlan = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkHealth();
        this.loadExamples();
        this.loadFunctionLibrary();
    }

    bindEvents() {
        // Button events
        document.getElementById('process-btn').addEventListener('click', () => this.processQuery());
        document.getElementById('execute-btn').addEventListener('click', () => this.executePlan());
        document.getElementById('dry-run-btn').addEventListener('click', () => this.dryRun());

        // Enter key in textarea
        document.getElementById('query-input').addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                this.processQuery();
            }
        });
    }

    async checkHealth() {
        try {
            const response = await fetch('/api/health');
            const health = await response.json();
            
            const statusDot = document.getElementById('status-dot');
            const statusText = document.getElementById('status-text');
            
            if (health.status === 'healthy') {
                statusDot.className = 'status-dot healthy';
                statusText.textContent = `System Ready (${health.functions_loaded} functions loaded)`;
            } else {
                statusDot.className = 'status-dot unhealthy';
                statusText.textContent = 'System Unavailable - Check Ollama service';
            }
        } catch (error) {
            console.error('Health check failed:', error);
            document.getElementById('status-dot').className = 'status-dot unhealthy';
            document.getElementById('status-text').textContent = 'Connection Error';
        }
    }

    async loadExamples() {
        try {
            const response = await fetch('/api/examples');
            const data = await response.json();
            
            const container = document.getElementById('examples-container');
            container.innerHTML = '';
            
            data.examples.forEach(example => {
                const card = document.createElement('div');
                card.className = 'example-card';
                card.innerHTML = `
                    <h4>${example.title}</h4>
                    <p>${example.description}</p>
                    <div class="query">"${example.query}"</div>
                `;
                
                card.addEventListener('click', () => {
                    document.getElementById('query-input').value = example.query;
                });
                
                container.appendChild(card);
            });
        } catch (error) {
            console.error('Failed to load examples:', error);
        }
    }

    async loadFunctionLibrary() {
        try {
            const response = await fetch('/api/functions');
            const data = await response.json();
            
            // Update stats
            const statsElement = document.getElementById('library-stats');
            statsElement.innerHTML = `
                <strong>${data.total_functions}</strong> functions across 
                <strong>${data.categories.length}</strong> categories
            `;
            
            // Display categories
            const container = document.getElementById('categories-grid');
            container.innerHTML = '';
            
            Object.entries(data.functions_by_category).forEach(([category, functions]) => {
                const card = document.createElement('div');
                card.className = 'category-card';
                
                const functionList = functions.slice(0, 5).map(f => f.name).join(', ');
                const moreText = functions.length > 5 ? ` and ${functions.length - 5} more...` : '';
                
                card.innerHTML = `
                    <h4>${category.replace('_', ' ').toUpperCase()}</h4>
                    <div class="function-count">${functions.length} functions</div>
                    <div class="function-list">${functionList}${moreText}</div>
                `;
                
                container.appendChild(card);
            });
        } catch (error) {
            console.error('Failed to load function library:', error);
        }
    }

    async processQuery() {
        const query = document.getElementById('query-input').value.trim();
        if (!query) {
            alert('Please enter a query');
            return;
        }

        this.showLoading('Analyzing your query and generating execution plan...');

        try {
            const response = await fetch('/api/process-query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ query })
            });

            const result = await response.json();

            if (result.success) {
                this.currentPlan = result.execution_plan;
                this.displayExecutionPlan(result);
                document.getElementById('execute-btn').disabled = false;
            } else {
                this.showError('Failed to process query', result.error);
            }
        } catch (error) {
            this.showError('Network error', error.message);
        } finally {
            this.hideLoading();
        }
    }

    async executePlan() {
        if (!this.currentPlan) {
            alert('No execution plan available');
            return;
        }

        this.showLoading('Executing function sequence...');

        try {
            const response = await fetch('/api/execute', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    execution_plan: this.currentPlan,
                    dry_run: false
                })
            });

            const result = await response.json();
            this.displayExecutionResults(result);
        } catch (error) {
            this.showError('Execution error', error.message);
        } finally {
            this.hideLoading();
        }
    }

    async dryRun() {
        const query = document.getElementById('query-input').value.trim();
        if (!query) {
            alert('Please enter a query');
            return;
        }

        this.showLoading('Running simulation...');

        try {
            const response = await fetch('/api/process-and-execute', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    query: query,
                    dry_run: true
                })
            });

            const result = await response.json();

            if (result.success) {
                this.displayFullResults(result);
            } else {
                this.showError('Dry run failed', result.error);
            }
        } catch (error) {
            this.showError('Network error', error.message);
        } finally {
            this.hideLoading();
        }
    }

    displayExecutionPlan(result) {
        const plan = result.execution_plan;
        
        // Show reasoning
        document.getElementById('reasoning').textContent = plan.reasoning;
        
        // Show function calls
        const callsContainer = document.getElementById('function-calls');
        callsContainer.innerHTML = '';
        
        plan.function_calls.forEach((call, index) => {
            const callElement = document.createElement('div');
            callElement.className = 'function-call';
            callElement.innerHTML = `
                <div class="step-number">${index + 1}</div>
                <h4>${call.function_name}</h4>
                <div class="parameters">${JSON.stringify(call.parameters, null, 2)}</div>
                ${call.output_variable ? `<div><strong>Output:</strong> ${call.output_variable}</div>` : ''}
                ${call.depends_on.length > 0 ? `<div><strong>Depends on:</strong> ${call.depends_on.join(', ')}</div>` : ''}
            `;
            callsContainer.appendChild(callElement);
        });
        
        // Show timing
        document.getElementById('processing-time').textContent = `Processing: ${result.processing_time.toFixed(2)}s`;
        document.getElementById('timing-info').style.display = 'flex';
        
        // Show results section
        document.getElementById('results-section').style.display = 'block';
        document.getElementById('results-section').scrollIntoView({ behavior: 'smooth' });
    }

    displayExecutionResults(result) {
        const container = document.getElementById('execution-output');
        container.innerHTML = '';
        
        Object.entries(result.results).forEach(([stepName, stepResult]) => {
            const resultElement = document.createElement('div');
            resultElement.className = `execution-result ${stepResult.success ? '' : 'error'}`;
            resultElement.innerHTML = `
                <h4>${stepResult.function_name} (${stepResult.execution_time.toFixed(3)}s)</h4>
                <div class="result-data">${JSON.stringify(stepResult.result, null, 2)}</div>
            `;
            container.appendChild(resultElement);
        });
        
        if (result.errors.length > 0) {
            const errorsElement = document.createElement('div');
            errorsElement.className = 'execution-result error';
            errorsElement.innerHTML = `
                <h4>Errors</h4>
                <div class="result-data">${result.errors.join('\n')}</div>
            `;
            container.appendChild(errorsElement);
        }
        
        // Update timing
        document.getElementById('execution-time').textContent = `Execution: ${result.execution_time.toFixed(2)}s`;
        
        // Show execution results
        document.getElementById('execution-results').style.display = 'block';
    }

    displayFullResults(result) {
        // Display execution plan
        this.displayExecutionPlan({
            execution_plan: result.execution_plan,
            processing_time: result.processing_time
        });
        
        // Display execution results
        this.displayExecutionResults({
            results: result.execution_results,
            errors: result.errors,
            execution_time: result.execution_time
        });
        
        // Update total timing
        document.getElementById('total-time').textContent = `Total: ${result.total_time.toFixed(2)}s`;
    }

    showLoading(message) {
        document.getElementById('loading-text').textContent = message;
        document.getElementById('loading-overlay').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loading-overlay').style.display = 'none';
    }

    showError(title, message) {
        alert(`${title}: ${message}`);
    }
}

// Initialize the UI when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new PipelineUI();
});
