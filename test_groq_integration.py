#!/usr/bin/env python3
"""
Test Groq integration with the AI Function Calling Pipeline.
"""

import sys
import os
import time
from pathlib import Path

# Setup paths
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

def test_groq_setup():
    """Test if Groq API key is available."""
    api_key = os.getenv("GROQ_API_KEY")
    if api_key:
        print(f"✅ GROQ_API_KEY found: {api_key[:8]}...")
        return True
    else:
        print("❌ GROQ_API_KEY not found")
        print("   Set environment variable: set GROQ_API_KEY=your_api_key")
        return False

def test_groq_client():
    """Test Groq client functionality."""
    try:
        from src.pipeline.groq_client import GroqClient
        
        print("🤖 Testing Groq client...")
        client = GroqClient()
        
        if client.is_available():
            print(f"✅ Groq API available with model: {client.model}")
            
            # Test simple completion
            messages = [{"role": "user", "content": "Hello! Respond with just 'Hi there!'"}]
            response = client.chat_completion(messages, max_tokens=10)
            print(f"✅ Test response: {response}")
            return True
        else:
            print("❌ Groq API not available")
            return False
            
    except Exception as e:
        print(f"❌ Groq client error: {e}")
        return False

def test_pipeline_with_groq():
    """Test the pipeline with Groq integration."""
    try:
        from src.pipeline.core import FunctionCallingPipeline
        from src.models.schemas import QueryRequest
        
        print("🚀 Testing pipeline with Groq...")
        
        # Initialize pipeline with Groq
        pipeline = FunctionCallingPipeline(use_groq=True)
        health = pipeline.get_health_status()
        
        print(f"Pipeline status: {health['status']}")
        print(f"AI Provider: {health['ai_provider']}")
        print(f"Model: {health['model']}")
        print(f"Model available: {health['model_available']}")
        print(f"Functions loaded: {health['functions_loaded']}")
        
        if health['model_available']:
            print("\n🔥 Testing fast AI query processing...")
            
            # Test query
            query = "Send an <NAME_EMAIL> with subject 'Test Message'"
            print(f"Query: {query}")
            
            start_time = time.time()
            request = QueryRequest(query=query)
            response = pipeline.process_query(request)
            processing_time = time.time() - start_time
            
            if response.success:
                print(f"✅ SUCCESS! Processed in {processing_time:.2f} seconds")
                print(f"Reasoning: {response.execution_plan.reasoning}")
                print("Function calls:")
                for i, call in enumerate(response.execution_plan.function_calls, 1):
                    print(f"  {i}. {call.function_name}")
                    print(f"     Parameters: {call.parameters}")
                return True
            else:
                print(f"❌ Query failed: {response.error}")
                return False
        else:
            print("⚠️ AI model not available")
            return False
            
    except Exception as e:
        print(f"❌ Pipeline error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_speed_comparison():
    """Compare Groq vs Ollama speed (if both available)."""
    print("\n⚡ Speed Comparison Test...")
    
    try:
        from src.pipeline.core import FunctionCallingPipeline
        from src.models.schemas import QueryRequest
        
        query = "Get system status"
        
        # Test Groq
        if os.getenv("GROQ_API_KEY"):
            print("Testing Groq speed...")
            pipeline_groq = FunctionCallingPipeline(use_groq=True)
            if pipeline_groq.is_ready():
                start_time = time.time()
                request = QueryRequest(query=query)
                response = pipeline_groq.process_query(request)
                groq_time = time.time() - start_time
                print(f"✅ Groq processing time: {groq_time:.2f} seconds")
            else:
                print("❌ Groq not available")
                groq_time = None
        else:
            print("⚠️ Groq API key not set")
            groq_time = None
        
        # Test Ollama (if available)
        print("Testing Ollama speed...")
        pipeline_ollama = FunctionCallingPipeline(use_groq=False)
        if pipeline_ollama.is_ready():
            start_time = time.time()
            request = QueryRequest(query=query)
            response = pipeline_ollama.process_query(request)
            ollama_time = time.time() - start_time
            print(f"✅ Ollama processing time: {ollama_time:.2f} seconds")
        else:
            print("❌ Ollama not available")
            ollama_time = None
        
        # Compare
        if groq_time and ollama_time:
            speedup = ollama_time / groq_time
            print(f"\n🏆 Groq is {speedup:.1f}x faster than Ollama!")
        elif groq_time:
            print(f"\n🚀 Groq processed query in {groq_time:.2f} seconds")
        elif ollama_time:
            print(f"\n🐌 Ollama processed query in {ollama_time:.2f} seconds")
        
    except Exception as e:
        print(f"❌ Speed test error: {e}")

def main():
    """Run all tests."""
    print("🚀 GROQ INTEGRATION TEST")
    print("=" * 40)
    
    # Test 1: Check API key
    print("\n1. Checking Groq API Key...")
    api_key_ok = test_groq_setup()
    
    if not api_key_ok:
        print("\n❌ Cannot proceed without GROQ_API_KEY")
        print("\nTo get a Groq API key:")
        print("1. Visit: https://console.groq.com/")
        print("2. Sign up for free account")
        print("3. Generate API key")
        print("4. Set environment variable: set GROQ_API_KEY=your_key")
        return
    
    # Test 2: Groq client
    print("\n2. Testing Groq Client...")
    client_ok = test_groq_client()
    
    # Test 3: Pipeline integration
    print("\n3. Testing Pipeline Integration...")
    pipeline_ok = test_pipeline_with_groq()
    
    # Test 4: Speed comparison
    if pipeline_ok:
        test_speed_comparison()
    
    # Summary
    print("\n" + "=" * 40)
    print("TEST SUMMARY:")
    print(f"  API Key: {'✅' if api_key_ok else '❌'}")
    print(f"  Groq Client: {'✅' if client_ok else '❌'}")
    print(f"  Pipeline Integration: {'✅' if pipeline_ok else '❌'}")
    
    if all([api_key_ok, client_ok, pipeline_ok]):
        print("\n🎉 ALL TESTS PASSED!")
        print("Groq integration is working perfectly!")
        print("\nNext steps:")
        print("  • Start server: python start_groq_server.py")
        print("  • Open web interface: http://localhost:8000")
        print("  • Enjoy lightning-fast AI responses! ⚡")
    else:
        print("\n⚠️ Some tests failed. Check the output above.")

if __name__ == "__main__":
    main()
