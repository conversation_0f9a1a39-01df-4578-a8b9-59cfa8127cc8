#!/usr/bin/env python3
"""
Test Llama 3.2 integration with the AI Function Calling Pipeline.
"""

import sys
import json
import requests
from pathlib import Path

# Setup paths
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

def test_ollama_connection():
    """Test if Ollama is running and accessible."""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json()
            print("✅ Ollama is running")
            print("Available models:")
            for model in models.get("models", []):
                print(f"  • {model['name']} ({model['details']['parameter_size']})")
            return True
        else:
            print("❌ Ollama not responding correctly")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to Ollama: {e}")
        return False

def test_llama_generation():
    """Test Llama 3.2 text generation."""
    try:
        payload = {
            "model": "llama3.2:latest",
            "prompt": "Hello! Can you help me with function calling?",
            "stream": False,
            "options": {
                "temperature": 0.1,
                "max_tokens": 100
            }
        }
        
        print("🤖 Testing Llama 3.2 generation...")
        response = requests.post(
            "http://localhost:11434/api/generate",
            json=payload,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            ai_response = result.get("response", "")
            print(f"✅ Llama 3.2 Response: {ai_response[:150]}...")
            return True
        else:
            print(f"❌ Generation failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Generation error: {e}")
        return False

def test_pipeline_integration():
    """Test the pipeline with Llama 3.2."""
    try:
        from src.pipeline.ai_client import OllamaClient
        from src.pipeline.core import FunctionCallingPipeline
        
        print("🔧 Testing pipeline integration...")
        
        # Test client
        client = OllamaClient()
        print(f"Client model: {client.model}")
        print(f"Ollama available: {client.is_available()}")
        
        # Test pipeline
        pipeline = FunctionCallingPipeline()
        health = pipeline.get_health_status()
        
        print(f"Pipeline status: {health['status']}")
        print(f"Model available: {health['model_available']}")
        print(f"Functions loaded: {health['functions_loaded']}")
        
        if health['model_available']:
            print("✅ Pipeline ready for AI-powered queries!")
            return True
        else:
            print("⚠️ Pipeline working but AI model not detected")
            return False
            
    except Exception as e:
        print(f"❌ Pipeline integration error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_function_calling():
    """Test function calling with Llama 3.2."""
    try:
        from src.pipeline.core import FunctionCallingPipeline
        from src.models.schemas import QueryRequest
        
        print("🚀 Testing function calling...")
        
        pipeline = FunctionCallingPipeline()
        
        # Test query
        query = "Retrieve all invoices for March and calculate the total amount"
        request = QueryRequest(query=query)
        
        print(f"Query: {query}")
        
        response = pipeline.process_query(request)
        
        if response.success:
            print("✅ Query processed successfully!")
            print(f"Reasoning: {response.execution_plan.reasoning}")
            print(f"Function calls: {len(response.execution_plan.function_calls)}")
            
            for i, call in enumerate(response.execution_plan.function_calls, 1):
                print(f"  {i}. {call.function_name}({call.parameters})")
            
            return True
        else:
            print(f"❌ Query processing failed: {response.error}")
            return False
            
    except Exception as e:
        print(f"❌ Function calling error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🤖 LLAMA 3.2 INTEGRATION TEST")
    print("=" * 40)
    
    # Test 1: Ollama connection
    print("\n1. Testing Ollama Connection...")
    ollama_ok = test_ollama_connection()
    
    if not ollama_ok:
        print("\n❌ Ollama not available. Please ensure:")
        print("  • Ollama is running: ollama serve")
        print("  • Llama 3.2 is available: ollama run llama3.2")
        return
    
    # Test 2: Llama generation
    print("\n2. Testing Llama 3.2 Generation...")
    generation_ok = test_llama_generation()
    
    # Test 3: Pipeline integration
    print("\n3. Testing Pipeline Integration...")
    pipeline_ok = test_pipeline_integration()
    
    # Test 4: Function calling (if AI is available)
    if pipeline_ok:
        print("\n4. Testing Function Calling...")
        function_calling_ok = test_function_calling()
    else:
        print("\n4. Skipping function calling test (AI not available)")
        function_calling_ok = False
    
    # Summary
    print("\n" + "=" * 40)
    print("TEST SUMMARY:")
    print(f"  Ollama Connection: {'✅' if ollama_ok else '❌'}")
    print(f"  Llama Generation: {'✅' if generation_ok else '❌'}")
    print(f"  Pipeline Integration: {'✅' if pipeline_ok else '❌'}")
    print(f"  Function Calling: {'✅' if function_calling_ok else '❌'}")
    
    if all([ollama_ok, generation_ok, pipeline_ok, function_calling_ok]):
        print("\n🎉 ALL TESTS PASSED!")
        print("The AI Function Calling Pipeline is ready with Llama 3.2!")
        print("\nNext steps:")
        print("  • Start the server: python start_main_server.py")
        print("  • Open web interface: http://localhost:8000")
        print("  • Try natural language queries!")
    else:
        print("\n⚠️ Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
