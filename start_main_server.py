#!/usr/bin/env python3
"""
Start the main AI Function Calling Pipeline server.
"""

import sys
import os
from pathlib import Path

# Setup paths
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))
os.chdir(project_root)

print("AI Function Calling Pipeline Server")
print("===================================")

try:
    print("Loading components...")
    
    # Import the main app
    from src.api.routes import app
    import uvicorn
    
    print("Components loaded successfully!")
    print()
    print("Starting server...")
    print("URL: http://127.0.0.1:8000")
    print("API Docs: http://127.0.0.1:8000/docs")
    print("Health Check: http://127.0.0.1:8000/api/health")
    print()
    print("Press Ctrl+C to stop the server")
    print()
    
    # Start the main server
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8000,
        log_level="info",
        access_log=True
    )
    
except KeyboardInterrupt:
    print("\nServer stopped by user")
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
