#!/usr/bin/env python3
"""
Demo script that shows the AI Function Calling Pipeline functionality
without requiring a running server.
"""

import sys
import json
from pathlib import Path

# Setup paths
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

def print_separator(title=""):
    """Print a visual separator."""
    print("\n" + "="*60)
    if title:
        print(f" {title} ")
        print("="*60)
    print()

def main():
    print_separator("AI Function Calling Pipeline Demo")
    print("This demo showcases the pipeline's core functionality")
    print("without requiring Ollama or a running server.")
    
    try:
        # Test function registry
        print("\n🔧 Loading function registry...")
        from src.functions.registry import function_registry
        
        functions = function_registry.list_functions()
        categories = function_registry.get_categories()
        
        print(f"✅ Loaded {len(functions)} functions")
        print(f"✅ Found {len(categories)} categories")
        
        # Show categories
        print("\n📚 Function Categories:")
        for category in categories:
            category_functions = function_registry.get_functions_by_category(category)
            print(f"  • {category}: {len(category_functions)} functions")
        
        # Test some functions
        print("\n🧪 Testing sample functions...")
        
        # Test data operations
        from src.functions.data_operations import retrieve_data, aggregate_data
        test_data = retrieve_data("test_source")
        print(f"✅ retrieve_data: returned {len(test_data)} records")
        
        total = aggregate_data(test_data, "value", "sum")
        print(f"✅ aggregate_data: calculated sum = {total}")
        
        # Test communication
        from src.functions.communication import send_email
        email_result = send_email("<EMAIL>", "Test Subject", "Test Body")
        print(f"✅ send_email: {email_result['status']}")
        
        # Test financial
        from src.functions.financial import retrieve_invoices, calculate_total_amount
        invoices = retrieve_invoices("March", 2024)
        total_amount = calculate_total_amount(invoices)
        print(f"✅ financial functions: {len(invoices)} invoices, total: ${total_amount}")
        
        # Test pipeline core
        print("\n🚀 Testing pipeline core...")
        from src.pipeline.core import FunctionCallingPipeline
        
        pipeline = FunctionCallingPipeline()
        library = pipeline.get_function_library()
        health = pipeline.get_health_status()
        
        print(f"✅ Pipeline initialized")
        print(f"✅ Health status: {health['status']}")
        print(f"✅ Functions available: {health['functions_loaded']}")
        print(f"✅ Model available: {health['model_available']}")
        
        # Show example execution plan
        print_separator("Example Execution Plan")
        print("Query: 'Retrieve all invoices for March, calculate total, and send email'")
        print("\nGenerated Plan (simulated):")
        
        example_plan = {
            "reasoning": "User wants to process March invoices, calculate total, and email results",
            "function_calls": [
                {
                    "function_name": "retrieve_invoices",
                    "parameters": {"month": "March", "year": 2024},
                    "output_variable": "march_invoices"
                },
                {
                    "function_name": "calculate_total_amount",
                    "parameters": {"invoices": "march_invoices"},
                    "output_variable": "total_amount"
                },
                {
                    "function_name": "send_email",
                    "parameters": {
                        "to": "<EMAIL>",
                        "subject": "March Invoice Summary",
                        "body": "Total amount: ${total_amount}"
                    }
                }
            ]
        }
        
        print(json.dumps(example_plan, indent=2))
        
        # Simulate execution
        print_separator("Simulated Execution")
        print("Executing the plan...")
        
        # Step 1
        print("1. retrieve_invoices(month='March', year=2024)")
        invoices = retrieve_invoices("March", 2024)
        print(f"   → Retrieved {len(invoices)} invoices")
        
        # Step 2
        print("2. calculate_total_amount(invoices=march_invoices)")
        total = calculate_total_amount(invoices)
        print(f"   → Total amount: ${total}")
        
        # Step 3
        print("3. send_email(...)")
        email_result = send_email("<EMAIL>", "March Invoice Summary", f"Total: ${total}")
        print(f"   → Email sent: {email_result['message_id']}")
        
        print_separator("Demo Complete")
        print("✅ All components working correctly!")
        print("✅ Function library fully operational")
        print("✅ Pipeline core functioning")
        print("✅ Example workflow executed successfully")
        
        print("\nTo enable full AI functionality:")
        print("1. Install Ollama: python setup_ollama.py")
        print("2. Start server: python start_server.py")
        print("3. Open web interface: http://localhost:8000")
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
