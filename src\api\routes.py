"""
FastAPI routes for the AI function calling pipeline.
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from src.models.schemas import (
    QueryRequest, QueryResponse, ExecutionRequest, ExecutionResult, HealthStatus
)
from src.pipeline.core import FunctionCallingPipeline
import logging
import os
from pathlib import Path

logger = logging.getLogger(__name__)

# Initialize the pipeline
pipeline = FunctionCallingPipeline()

# Create FastAPI app
app = FastAPI(
    title="AI Function Calling Pipeline",
    description="A pipeline that converts natural language queries into structured function call sequences",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
static_dir = Path(__file__).parent.parent.parent / "web"
if static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")


@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the main web interface."""
    static_dir = Path(__file__).parent.parent.parent / "web"
    index_file = static_dir / "index.html"
    
    if index_file.exists():
        return HTMLResponse(content=index_file.read_text(), status_code=200)
    else:
        return HTMLResponse(
            content="""
            <html>
                <head><title>AI Function Calling Pipeline</title></head>
                <body>
                    <h1>AI Function Calling Pipeline</h1>
                    <p>Web interface not found. Please check the web directory.</p>
                    <p>API Documentation: <a href="/docs">/docs</a></p>
                </body>
            </html>
            """,
            status_code=200
        )


@app.post("/api/process-query", response_model=QueryResponse)
async def process_query(request: QueryRequest):
    """Process a natural language query and return execution plan."""
    try:
        logger.info(f"Received query: {request.query}")
        response = pipeline.process_query(request)
        return response
    except Exception as e:
        logger.error(f"Error processing query: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/execute", response_model=ExecutionResult)
async def execute_plan(request: ExecutionRequest):
    """Execute a function call sequence."""
    try:
        logger.info(f"Executing plan with {len(request.execution_plan.function_calls)} function calls")
        result = pipeline.execute_plan(request)
        return result
    except Exception as e:
        logger.error(f"Error executing plan: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/process-and-execute")
async def process_and_execute(request: dict):
    """Process query and execute plan in one call."""
    try:
        query = request.get("query", "")
        dry_run = request.get("dry_run", False)
        
        if not query:
            raise HTTPException(status_code=400, detail="Query is required")
        
        logger.info(f"Processing and executing query: {query}")
        result = pipeline.process_and_execute(query, dry_run)
        return result
    except Exception as e:
        logger.error(f"Error in process and execute: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/functions")
async def get_functions():
    """Get all available functions with their schemas."""
    try:
        return pipeline.get_function_library()
    except Exception as e:
        logger.error(f"Error getting functions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/health", response_model=HealthStatus)
async def health_check():
    """Health check endpoint."""
    try:
        status = pipeline.get_health_status()
        return HealthStatus(
            status=status["status"],
            model_available=status["model_available"],
            functions_loaded=status["functions_loaded"],
            uptime=status["uptime"]
        )
    except Exception as e:
        logger.error(f"Error in health check: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/examples")
async def get_examples():
    """Get example queries for testing."""
    return {
        "examples": [
            {
                "title": "Invoice Processing",
                "query": "Retrieve all invoices for March, summarize the total amount, and send the summary to my email.",
                "description": "Demonstrates data retrieval, aggregation, and communication functions"
            },
            {
                "title": "System Monitoring",
                "query": "Check system status, create a backup if disk usage is high, and send a notification.",
                "description": "Shows system operations and conditional logic"
            },
            {
                "title": "Data Analysis",
                "query": "Get sales data for Q1, calculate statistics, create a chart, and generate a report.",
                "description": "Illustrates analytics and reporting workflow"
            },
            {
                "title": "File Management",
                "query": "List all PDF files in the documents folder, compress them into an archive, and move to backup location.",
                "description": "File operations and organization"
            },
            {
                "title": "Scheduled Tasks",
                "query": "Create a calendar event for next week's team meeting and set a reminder 30 minutes before.",
                "description": "Scheduling and reminder functions"
            },
            {
                "title": "Web Operations",
                "query": "Search for AI news, scrape the top 3 articles, and save summaries to a file.",
                "description": "Web scraping and content processing"
            },
            {
                "title": "Database Operations",
                "query": "Query user table for active users, update their last login, and backup the database.",
                "description": "Database CRUD operations and maintenance"
            },
            {
                "title": "Financial Reporting",
                "query": "Get all payments for this month, calculate tax amounts, and generate a financial report.",
                "description": "Financial calculations and reporting"
            }
        ]
    }


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error", "error": str(exc)}
    )


# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize the application."""
    logger.info("Starting AI Function Calling Pipeline")
    logger.info(f"Pipeline ready: {pipeline.is_ready()}")
    logger.info(f"Functions loaded: {len(pipeline.executor.get_available_functions())}")


# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    logger.info("Shutting down AI Function Calling Pipeline")
