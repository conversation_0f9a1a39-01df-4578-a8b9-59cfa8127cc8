#!/usr/bin/env python3
"""
Minimal server test to verify FastAPI works.
"""

from fastapi import FastAPI
import uvicorn

app = FastAPI(title="Test Server")

@app.get("/")
def read_root():
    return {"message": "Hello World", "status": "working"}

@app.get("/health")
def health_check():
    return {"status": "healthy", "server": "minimal test"}

if __name__ == "__main__":
    print("Starting minimal test server...")
    print("URL: http://127.0.0.1:8000")
    print("Health: http://127.0.0.1:8000/health")
    
    uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")
