"""
Data models and schemas for the AI function calling pipeline.
"""

from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum


class FunctionParameterType(str, Enum):
    """Supported parameter types for functions."""
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    LIST = "list"
    DICT = "dict"
    ANY = "any"


class FunctionParameter(BaseModel):
    """Schema for a function parameter."""
    name: str = Field(..., description="Parameter name")
    type: FunctionParameterType = Field(..., description="Parameter type")
    description: str = Field(..., description="Parameter description")
    required: bool = Field(True, description="Whether parameter is required")
    default: Optional[Any] = Field(None, description="Default value if not required")
    enum_values: Optional[List[str]] = Field(None, description="Allowed values for enum parameters")


class FunctionSchema(BaseModel):
    """Schema for a function definition."""
    name: str = Field(..., description="Function name")
    description: str = Field(..., description="Function description")
    category: str = Field(..., description="Function category")
    parameters: List[FunctionParameter] = Field(default_factory=list, description="Function parameters")
    returns: str = Field(..., description="Description of return value")
    examples: List[str] = Field(default_factory=list, description="Usage examples")


class FunctionCall(BaseModel):
    """Schema for a function call in the execution sequence."""
    function_name: str = Field(..., description="Name of function to call")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Function parameters")
    output_variable: Optional[str] = Field(None, description="Variable name to store output")
    depends_on: List[str] = Field(default_factory=list, description="Variables this call depends on")


class ExecutionPlan(BaseModel):
    """Schema for the complete execution plan."""
    query: str = Field(..., description="Original user query")
    reasoning: str = Field(..., description="AI reasoning for the plan")
    function_calls: List[FunctionCall] = Field(..., description="Sequence of function calls")
    estimated_duration: Optional[float] = Field(None, description="Estimated execution time in seconds")


class QueryRequest(BaseModel):
    """Schema for incoming query requests."""
    query: str = Field(..., description="User's natural language query")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")
    user_id: Optional[str] = Field(None, description="User identifier")


class QueryResponse(BaseModel):
    """Schema for query response."""
    success: bool = Field(..., description="Whether processing was successful")
    execution_plan: Optional[ExecutionPlan] = Field(None, description="Generated execution plan")
    error: Optional[str] = Field(None, description="Error message if failed")
    processing_time: float = Field(..., description="Time taken to process query")


class ExecutionRequest(BaseModel):
    """Schema for execution requests."""
    execution_plan: ExecutionPlan = Field(..., description="Plan to execute")
    dry_run: bool = Field(False, description="Whether to simulate execution")


class ExecutionResult(BaseModel):
    """Schema for execution results."""
    success: bool = Field(..., description="Whether execution was successful")
    results: Dict[str, Any] = Field(default_factory=dict, description="Results from each function call")
    errors: List[str] = Field(default_factory=list, description="Any errors encountered")
    execution_time: float = Field(..., description="Total execution time")


class FunctionResult(BaseModel):
    """Schema for individual function execution result."""
    function_name: str = Field(..., description="Name of executed function")
    success: bool = Field(..., description="Whether function executed successfully")
    result: Any = Field(None, description="Function result")
    error: Optional[str] = Field(None, description="Error message if failed")
    execution_time: float = Field(..., description="Function execution time")


class HealthStatus(BaseModel):
    """Schema for health check response."""
    status: str = Field(..., description="Service status")
    model_available: bool = Field(..., description="Whether AI model is available")
    functions_loaded: int = Field(..., description="Number of functions loaded")
    uptime: float = Field(..., description="Service uptime in seconds")
