#!/usr/bin/env python3
"""
Final test of the AI Function Calling Pipeline.
"""

import sys
from pathlib import Path

# Setup paths
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))

print("AI FUNCTION CALLING PIPELINE - FINAL STATUS")
print("=" * 50)

try:
    # Test function registry
    from src.functions.registry import function_registry
    functions = function_registry.list_functions()
    categories = function_registry.get_categories()
    
    print("FUNCTION LIBRARY:")
    print(f"  Total Functions: {len(functions)}")
    print(f"  Categories: {len(categories)}")
    
    # Test some key functions
    from src.functions.data_operations import retrieve_data, aggregate_data
    from src.functions.communication import send_email
    from src.functions.financial import retrieve_invoices, calculate_total_amount
    
    print("\nFUNCTION TESTS:")
    
    # Data operations
    data = retrieve_data('test_source')
    print(f"  retrieve_data: {len(data)} records")
    
    total = aggregate_data(data, 'value', 'sum')
    print(f"  aggregate_data: sum = {total}")
    
    # Communication
    email_result = send_email('<EMAIL>', 'Test Subject', 'Test message')
    print(f"  send_email: {email_result['status']}")
    
    # Financial
    invoices = retrieve_invoices('March', 2024)
    amount = calculate_total_amount(invoices)
    print(f"  financial: {len(invoices)} invoices, ${amount}")
    
    # Test pipeline core
    from src.pipeline.core import FunctionCallingPipeline
    pipeline = FunctionCallingPipeline()
    health = pipeline.get_health_status()
    
    print("\nPIPELINE STATUS:")
    print(f"  Status: {health['status']}")
    print(f"  Functions Available: {health['functions_loaded']}")
    print(f"  Model Available: {health['model_available']}")
    print(f"  Uptime: {health['uptime']:.1f} seconds")
    
    print("\nSYSTEM READY:")
    print("  Web Interface: http://127.0.0.1:8000")
    print("  API Documentation: http://127.0.0.1:8000/docs")
    print("  Health Check: http://127.0.0.1:8000/api/health")
    
    print("\nALL SYSTEMS OPERATIONAL!")
    
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
