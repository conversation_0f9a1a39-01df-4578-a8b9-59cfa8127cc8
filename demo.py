#!/usr/bin/env python3
"""
Demo script for the AI Function Calling Pipeline.
This script demonstrates the pipeline's capabilities with various example queries.
"""

import sys
import time
import json
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.pipeline.core import FunctionCallingPipeline
from src.models.schemas import QueryRequest


def print_separator(title=""):
    """Print a visual separator."""
    print("\n" + "="*60)
    if title:
        print(f" {title} ")
        print("="*60)
    print()


def print_json(data, title=""):
    """Pretty print JSON data."""
    if title:
        print(f"{title}:")
    print(json.dumps(data, indent=2, default=str))
    print()


def demo_query(pipeline, query, description=""):
    """Demonstrate a single query."""
    print_separator(f"Demo: {description}" if description else "Demo Query")
    print(f"Query: {query}")
    print()
    
    # Process the query
    print("🔄 Processing query...")
    start_time = time.time()
    
    request = QueryRequest(query=query)
    response = pipeline.process_query(request)
    
    processing_time = time.time() - start_time
    
    if response.success:
        print(f"✅ Query processed successfully in {processing_time:.2f}s")
        print()
        
        # Display the execution plan
        plan = response.execution_plan
        print("📋 Execution Plan:")
        print(f"Reasoning: {plan.reasoning}")
        print()
        
        print("Function Calls:")
        for i, call in enumerate(plan.function_calls, 1):
            print(f"  {i}. {call.function_name}")
            print(f"     Parameters: {call.parameters}")
            if call.output_variable:
                print(f"     Output Variable: {call.output_variable}")
            if call.depends_on:
                print(f"     Depends On: {call.depends_on}")
            print()
        
        # Execute the plan (dry run)
        print("🧪 Executing plan (dry run)...")
        result = pipeline.process_and_execute(query, dry_run=True)
        
        if result["success"]:
            print("✅ Execution simulation completed")
            print(f"Total time: {result['total_time']:.2f}s")
            
            if result["execution_results"]:
                print("\nExecution Results:")
                for step_name, step_result in result["execution_results"].items():
                    print(f"  {step_result['function_name']}: {step_result['result']}")
        else:
            print(f"❌ Execution failed: {result.get('error', 'Unknown error')}")
    
    else:
        print(f"❌ Query processing failed: {response.error}")
    
    print()


def main():
    """Run the demo."""
    print_separator("AI Function Calling Pipeline Demo")
    print("This demo showcases the pipeline's ability to convert natural language")
    print("queries into structured function call sequences.")
    print()
    
    # Initialize pipeline
    print("🚀 Initializing pipeline...")
    pipeline = FunctionCallingPipeline()
    
    # Check if pipeline is ready
    if not pipeline.is_ready():
        print("❌ Pipeline not ready. Please ensure:")
        print("  1. Ollama is installed and running (ollama serve)")
        print("  2. Qwen 2.5 7B model is downloaded (ollama pull qwen2.5:7b)")
        print("  3. The model is accessible at http://localhost:11434")
        print()
        print("Run the setup script: python setup_ollama.py")
        sys.exit(1)
    
    print("✅ Pipeline ready!")
    
    # Show function library stats
    library = pipeline.get_function_library()
    print(f"📚 Function Library: {library['total_functions']} functions across {len(library['categories'])} categories")
    print(f"Categories: {', '.join(library['categories'])}")
    print()
    
    # Demo queries
    demo_queries = [
        {
            "query": "Retrieve all invoices for March, summarize the total amount, and send the summary to my email.",
            "description": "Invoice Processing Workflow"
        },
        {
            "query": "Check system status, create a backup if disk usage is high, and send a notification.",
            "description": "System Monitoring and Maintenance"
        },
        {
            "query": "Get sales data for Q1, calculate statistics, create a chart, and generate a report.",
            "description": "Data Analysis and Reporting"
        },
        {
            "query": "List all PDF files in documents folder, compress them, and move to backup location.",
            "description": "File Management Automation"
        },
        {
            "query": "Create a calendar event for team meeting next Tuesday and set a reminder.",
            "description": "Scheduling and Reminders"
        },
        {
            "query": "Search for AI news, scrape top 3 articles, and save summaries to a file.",
            "description": "Web Scraping and Content Processing"
        }
    ]
    
    # Run demos
    for i, demo in enumerate(demo_queries, 1):
        print(f"Demo {i}/{len(demo_queries)}")
        demo_query(pipeline, demo["query"], demo["description"])
        
        if i < len(demo_queries):
            input("Press Enter to continue to next demo...")
    
    # Interactive mode
    print_separator("Interactive Mode")
    print("Now you can try your own queries!")
    print("Type 'quit' to exit, 'help' for examples, or enter a natural language query.")
    print()
    
    while True:
        try:
            query = input("🤖 Enter your query: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                break
            elif query.lower() == 'help':
                print("\nExample queries:")
                for demo in demo_queries:
                    print(f"  • {demo['query']}")
                print()
                continue
            elif not query:
                continue
            
            demo_query(pipeline, query, "Your Query")
            
        except KeyboardInterrupt:
            print("\n\nGoodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print_separator("Demo Complete")
    print("Thank you for trying the AI Function Calling Pipeline!")
    print("For more information, visit the web interface at http://localhost:8000")
    print("or check the API documentation at http://localhost:8000/docs")


if __name__ == "__main__":
    main()
