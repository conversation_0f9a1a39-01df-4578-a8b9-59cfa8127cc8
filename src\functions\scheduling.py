"""Scheduling functions for the AI pipeline."""

from typing import Any, Dict, List, Optional
from src.models.schemas import FunctionSchema, FunctionParameter, FunctionParameterType
from datetime import datetime, timedelta


def create_calendar_event(title: str, start_time: str, end_time: str, description: str = "", attendees: List[str] = None) -> Dict[str, Any]:
    """Create a calendar event."""
    return {
        "success": True,
        "event_id": f"event_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "title": title,
        "start_time": start_time,
        "end_time": end_time,
        "attendees_count": len(attendees) if attendees else 0,
        "created_at": datetime.now().isoformat()
    }


def schedule_task(task_name: str, scheduled_time: str, priority: str = "normal") -> Dict[str, Any]:
    """Schedule a task for execution."""
    return {
        "success": True,
        "task_id": f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "task_name": task_name,
        "scheduled_time": scheduled_time,
        "priority": priority,
        "status": "scheduled"
    }


def create_reminder(message: str, remind_at: str, repeat: str = "none") -> Dict[str, Any]:
    """Create a reminder."""
    return {
        "success": True,
        "reminder_id": f"reminder_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "message": message,
        "remind_at": remind_at,
        "repeat": repeat,
        "created_at": datetime.now().isoformat()
    }


def get_schedule(date: str, user_id: str = None) -> List[Dict[str, Any]]:
    """Get schedule for a specific date."""
    return [
        {"time": "09:00", "title": "Team Meeting", "type": "event"},
        {"time": "14:00", "title": "Project Review", "type": "task"},
        {"time": "16:30", "title": "Call client", "type": "reminder"}
    ]


def cancel_event(event_id: str) -> Dict[str, Any]:
    """Cancel a scheduled event."""
    return {
        "success": True,
        "event_id": event_id,
        "status": "cancelled",
        "cancelled_at": datetime.now().isoformat()
    }


def register_functions(registry):
    """Register scheduling functions."""
    
    registry.register_function(
        FunctionSchema(name="create_calendar_event", description="Create calendar event", category="scheduling",
                      parameters=[FunctionParameter(name="title", type=FunctionParameterType.STRING, description="Event title"),
                                FunctionParameter(name="start_time", type=FunctionParameterType.STRING, description="Start time (ISO format)"),
                                FunctionParameter(name="end_time", type=FunctionParameterType.STRING, description="End time (ISO format)"),
                                FunctionParameter(name="description", type=FunctionParameterType.STRING, description="Event description", required=False),
                                FunctionParameter(name="attendees", type=FunctionParameterType.LIST, description="Attendee emails", required=False)],
                      returns="Event creation result", examples=["create_calendar_event('Team Meeting', '2024-03-15T09:00:00', '2024-03-15T10:00:00')"]),
        create_calendar_event
    )
    
    registry.register_function(
        FunctionSchema(name="schedule_task", description="Schedule a task", category="scheduling",
                      parameters=[FunctionParameter(name="task_name", type=FunctionParameterType.STRING, description="Task name"),
                                FunctionParameter(name="scheduled_time", type=FunctionParameterType.STRING, description="Scheduled time (ISO format)"),
                                FunctionParameter(name="priority", type=FunctionParameterType.STRING, description="Task priority", required=False, default="normal", enum_values=["low", "normal", "high", "urgent"])],
                      returns="Task scheduling result", examples=["schedule_task('Review documents', '2024-03-15T14:00:00', 'high')"]),
        schedule_task
    )
    
    registry.register_function(
        FunctionSchema(name="create_reminder", description="Create a reminder", category="scheduling",
                      parameters=[FunctionParameter(name="message", type=FunctionParameterType.STRING, description="Reminder message"),
                                FunctionParameter(name="remind_at", type=FunctionParameterType.STRING, description="Reminder time (ISO format)"),
                                FunctionParameter(name="repeat", type=FunctionParameterType.STRING, description="Repeat pattern", required=False, default="none", enum_values=["none", "daily", "weekly", "monthly"])],
                      returns="Reminder creation result", examples=["create_reminder('Call dentist', '2024-03-15T16:30:00', 'none')"]),
        create_reminder
    )
    
    registry.register_function(
        FunctionSchema(name="get_schedule", description="Get schedule for date", category="scheduling",
                      parameters=[FunctionParameter(name="date", type=FunctionParameterType.STRING, description="Date (YYYY-MM-DD)"),
                                FunctionParameter(name="user_id", type=FunctionParameterType.STRING, description="User ID", required=False)],
                      returns="Schedule items for the date", examples=["get_schedule('2024-03-15')"]),
        get_schedule
    )
    
    registry.register_function(
        FunctionSchema(name="cancel_event", description="Cancel scheduled event", category="scheduling",
                      parameters=[FunctionParameter(name="event_id", type=FunctionParameterType.STRING, description="Event ID to cancel")],
                      returns="Cancellation result", examples=["cancel_event('event_20240315_090000')"]),
        cancel_event
    )
