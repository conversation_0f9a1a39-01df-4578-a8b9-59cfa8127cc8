#!/usr/bin/env python3
"""
Debug server startup issues.
"""

import sys
import os
from pathlib import Path

# Setup paths
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))
os.chdir(project_root)

print("Debug: AI Function Calling Pipeline Server")
print("==========================================")

try:
    print("1. Testing FastAPI import...")
    from fastapi import FastAPI
    print("   OK: FastAPI imported")
    
    print("2. Testing uvicorn import...")
    import uvicorn
    print("   OK: Uvicorn imported")
    
    print("3. Testing our modules...")
    from src.functions.registry import function_registry
    print(f"   OK: Function registry: {len(function_registry.list_functions())} functions")
    
    from src.pipeline.core import FunctionCallingPipeline
    pipeline = FunctionCallingPipeline()
    print("   OK: Pipeline core loaded")
    
    from src.api.routes import app
    print("   OK: API routes loaded")
    
    print("4. Testing simple FastAPI app...")
    test_app = FastAPI()
    
    @test_app.get("/test")
    def test_endpoint():
        return {"status": "working", "message": "Test successful"}
    
    print("   OK: Test app created")
    
    print("5. Starting server on port 8001...")
    print("   URL: http://127.0.0.1:8001/test")
    print("   Press Ctrl+C to stop")
    
    uvicorn.run(test_app, host="127.0.0.1", port=8001, log_level="info")
    
except KeyboardInterrupt:
    print("\nServer stopped by user")
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
