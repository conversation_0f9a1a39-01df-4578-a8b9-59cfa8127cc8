"""System operations functions for the AI pipeline."""

from typing import Any, Dict, List, Optional
from src.models.schemas import FunctionSchema, FunctionParameter, FunctionParameterType
from datetime import datetime
import psutil


def get_system_status() -> Dict[str, Any]:
    """Get current system status and metrics."""
    try:
        return {
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_usage": psutil.disk_usage('/').percent,
            "uptime": "5 days, 3 hours",
            "status": "healthy",
            "timestamp": datetime.now().isoformat()
        }
    except:
        # Fallback mock data
        return {
            "cpu_percent": 25.5,
            "memory_percent": 68.2,
            "disk_usage": 45.8,
            "uptime": "5 days, 3 hours",
            "status": "healthy",
            "timestamp": datetime.now().isoformat()
        }


def create_log_entry(level: str, message: str, component: str = "system") -> Dict[str, Any]:
    """Create a log entry."""
    return {
        "success": True,
        "log_id": f"log_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "level": level,
        "message": message,
        "component": component,
        "timestamp": datetime.now().isoformat()
    }


def monitor_service(service_name: str) -> Dict[str, Any]:
    """Monitor a system service."""
    return {
        "service_name": service_name,
        "status": "running",
        "uptime": "2 days, 14 hours",
        "memory_usage": "256 MB",
        "cpu_usage": "2.5%",
        "last_restart": "2024-03-13T08:30:00",
        "monitored_at": datetime.now().isoformat()
    }


def create_backup(source: str, destination: str, backup_type: str = "full") -> Dict[str, Any]:
    """Create a system backup."""
    return {
        "success": True,
        "backup_id": f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "source": source,
        "destination": destination,
        "backup_type": backup_type,
        "size": "2.5 GB",
        "duration": "15 minutes",
        "created_at": datetime.now().isoformat()
    }


def restart_service(service_name: str) -> Dict[str, Any]:
    """Restart a system service."""
    return {
        "success": True,
        "service_name": service_name,
        "previous_status": "running",
        "new_status": "running",
        "restart_time": "3.2 seconds",
        "restarted_at": datetime.now().isoformat()
    }


def cleanup_temp_files(directory: str = "/tmp", older_than_days: int = 7) -> Dict[str, Any]:
    """Clean up temporary files."""
    return {
        "success": True,
        "directory": directory,
        "files_deleted": 47,
        "space_freed": "1.2 GB",
        "older_than_days": older_than_days,
        "cleaned_at": datetime.now().isoformat()
    }


def check_disk_space(path: str = "/") -> Dict[str, Any]:
    """Check disk space usage."""
    return {
        "path": path,
        "total_space": "500 GB",
        "used_space": "230 GB",
        "free_space": "270 GB",
        "usage_percent": 46.0,
        "status": "normal",
        "checked_at": datetime.now().isoformat()
    }


def register_functions(registry):
    """Register system operation functions."""
    
    registry.register_function(
        FunctionSchema(name="get_system_status", description="Get current system status", category="system_operations",
                      parameters=[],
                      returns="System status metrics", examples=["get_system_status()"]),
        get_system_status
    )
    
    registry.register_function(
        FunctionSchema(name="create_log_entry", description="Create a log entry", category="system_operations",
                      parameters=[FunctionParameter(name="level", type=FunctionParameterType.STRING, description="Log level", enum_values=["debug", "info", "warning", "error", "critical"]),
                                FunctionParameter(name="message", type=FunctionParameterType.STRING, description="Log message"),
                                FunctionParameter(name="component", type=FunctionParameterType.STRING, description="Component name", required=False, default="system")],
                      returns="Log entry result", examples=["create_log_entry('info', 'System backup completed', 'backup_service')"]),
        create_log_entry
    )
    
    registry.register_function(
        FunctionSchema(name="monitor_service", description="Monitor a system service", category="system_operations",
                      parameters=[FunctionParameter(name="service_name", type=FunctionParameterType.STRING, description="Service name to monitor")],
                      returns="Service monitoring data", examples=["monitor_service('nginx')"]),
        monitor_service
    )
    
    registry.register_function(
        FunctionSchema(name="create_backup", description="Create system backup", category="system_operations",
                      parameters=[FunctionParameter(name="source", type=FunctionParameterType.STRING, description="Source path"),
                                FunctionParameter(name="destination", type=FunctionParameterType.STRING, description="Backup destination"),
                                FunctionParameter(name="backup_type", type=FunctionParameterType.STRING, description="Backup type", required=False, default="full", enum_values=["full", "incremental", "differential"])],
                      returns="Backup creation result", examples=["create_backup('/var/www', '/backups/web', 'full')"]),
        create_backup
    )
    
    registry.register_function(
        FunctionSchema(name="restart_service", description="Restart a system service", category="system_operations",
                      parameters=[FunctionParameter(name="service_name", type=FunctionParameterType.STRING, description="Service name to restart")],
                      returns="Service restart result", examples=["restart_service('apache2')"]),
        restart_service
    )
    
    registry.register_function(
        FunctionSchema(name="cleanup_temp_files", description="Clean up temporary files", category="system_operations",
                      parameters=[FunctionParameter(name="directory", type=FunctionParameterType.STRING, description="Directory to clean", required=False, default="/tmp"),
                                FunctionParameter(name="older_than_days", type=FunctionParameterType.INTEGER, description="Delete files older than days", required=False, default=7)],
                      returns="Cleanup result", examples=["cleanup_temp_files('/tmp', 7)"]),
        cleanup_temp_files
    )
    
    registry.register_function(
        FunctionSchema(name="check_disk_space", description="Check disk space usage", category="system_operations",
                      parameters=[FunctionParameter(name="path", type=FunctionParameterType.STRING, description="Path to check", required=False, default="/")],
                      returns="Disk space information", examples=["check_disk_space('/var')"]),
        check_disk_space
    )
