"""
AI client for communicating with the Qwen 2.5 7B model via Ollama.
"""

import json
import requests
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


class OllamaClient:
    """Client for interacting with Ollama API."""
    
    def __init__(self, base_url: str = "http://localhost:11434", model: str = "llama3.2:latest"):
        self.base_url = base_url
        self.model = model
        self.session = requests.Session()
    
    def is_available(self) -> bool:
        """Check if Ollama service is available."""
        try:
            response = self.session.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Ollama service not available: {e}")
            return False
    
    def generate_completion(self, prompt: str, system_prompt: str = None, temperature: float = 0.1) -> str:
        """Generate completion from the model."""
        try:
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "top_p": 0.9,
                    "top_k": 40
                }
            }
            
            if system_prompt:
                payload["system"] = system_prompt
            
            response = self.session.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("response", "")
            else:
                logger.error(f"API error: {response.status_code} - {response.text}")
                return ""
                
        except Exception as e:
            logger.error(f"Error generating completion: {e}")
            return ""
    
    def chat_completion(self, messages: List[Dict[str, str]], temperature: float = 0.1) -> str:
        """Generate chat completion from the model."""
        try:
            payload = {
                "model": self.model,
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "top_p": 0.9,
                    "top_k": 40,
                    "num_predict": 500
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/api/chat",
                json=payload,
                timeout=120
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("message", {}).get("content", "")
            else:
                logger.error(f"API error: {response.status_code} - {response.text}")
                return ""
                
        except Exception as e:
            logger.error(f"Error generating chat completion: {e}")
            return ""


class AIPlanner:
    """AI-powered query planner using function calling capabilities."""
    
    def __init__(self, ollama_client: OllamaClient):
        self.client = ollama_client
        self.system_prompt = self._build_system_prompt()
    
    def _build_system_prompt(self) -> str:
        """Build the system prompt for function calling."""
        return """You are a function calling assistant. Convert user queries into JSON function call sequences.

TASK: Analyze the query and create a structured execution plan.

AVAILABLE FUNCTIONS:
- data_operations: retrieve_data, filter_data, aggregate_data, etc.
- communication: send_email, send_sms, send_notification
- financial: retrieve_invoices, calculate_total_amount, generate_invoice
- file_operations: read_file, write_file, copy_file
- analytics: calculate_statistics, create_chart, generate_report
- database: execute_query, backup_database
- scheduling: create_calendar_event, schedule_task
- system_operations: get_system_status, create_backup
- web_operations: make_http_request, scrape_webpage

RESPONSE FORMAT (JSON only):
{
    "reasoning": "Brief explanation",
    "function_calls": [
        {
            "function_name": "function_name",
            "parameters": {"param": "value"},
            "output_variable": "var_name",
            "depends_on": []
        }
    ]
}

RULES:
- Return ONLY valid JSON
- Use simple, clear variable names
- Include all required parameters
- Order functions logically"""
    
    def plan_execution(self, query: str, available_functions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Plan execution sequence for a user query."""
        
        # Build function reference for the prompt
        function_descriptions = []
        for func in available_functions:
            params = ", ".join([f"{p['name']}: {p['type']}" for p in func.get('parameters', [])])
            function_descriptions.append(f"- {func['name']}({params}): {func['description']}")
        
        functions_text = "\n".join(function_descriptions)
        
        prompt = f"""Convert this query to JSON function calls:

Query: "{query}"

Functions: {functions_text[:500]}...

Return ONLY this JSON format:
{{"reasoning": "brief explanation", "function_calls": [{{"function_name": "name", "parameters": {{}}, "output_variable": "var", "depends_on": []}}]}}"""
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": prompt}
        ]
        
        response = self.client.chat_completion(messages, temperature=0.2)
        
        try:
            # Try to parse JSON response
            if response.strip().startswith("```json"):
                response = response.strip()[7:-3]
            elif response.strip().startswith("```"):
                response = response.strip()[3:-3]
            
            plan = json.loads(response)
            
            # Validate the plan structure
            if "function_calls" not in plan:
                raise ValueError("Missing function_calls in response")
            
            return {
                "success": True,
                "plan": plan,
                "raw_response": response
            }
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Raw response: {response}")
            
            # Fallback: try to extract JSON from response
            try:
                start = response.find("{")
                end = response.rfind("}") + 1
                if start >= 0 and end > start:
                    json_part = response[start:end]
                    plan = json.loads(json_part)
                    return {
                        "success": True,
                        "plan": plan,
                        "raw_response": response
                    }
            except:
                pass
            
            return {
                "success": False,
                "error": f"Failed to parse AI response: {str(e)}",
                "raw_response": response
            }
        
        except Exception as e:
            logger.error(f"Error in plan execution: {e}")
            return {
                "success": False,
                "error": str(e),
                "raw_response": response
            }
