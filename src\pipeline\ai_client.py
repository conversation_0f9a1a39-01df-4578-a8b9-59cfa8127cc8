"""
AI client for communicating with the Qwen 2.5 7B model via Ollama.
"""

import json
import requests
from typing import Dict, Any, List, Optional
import logging

logger = logging.getLogger(__name__)


class OllamaClient:
    """Client for interacting with Ollama API."""
    
    def __init__(self, base_url: str = "http://localhost:11434", model: str = "qwen2.5:7b"):
        self.base_url = base_url
        self.model = model
        self.session = requests.Session()
    
    def is_available(self) -> bool:
        """Check if Ollama service is available."""
        try:
            response = self.session.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Ollama service not available: {e}")
            return False
    
    def generate_completion(self, prompt: str, system_prompt: str = None, temperature: float = 0.1) -> str:
        """Generate completion from the model."""
        try:
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "top_p": 0.9,
                    "top_k": 40
                }
            }
            
            if system_prompt:
                payload["system"] = system_prompt
            
            response = self.session.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("response", "")
            else:
                logger.error(f"API error: {response.status_code} - {response.text}")
                return ""
                
        except Exception as e:
            logger.error(f"Error generating completion: {e}")
            return ""
    
    def chat_completion(self, messages: List[Dict[str, str]], temperature: float = 0.1) -> str:
        """Generate chat completion from the model."""
        try:
            payload = {
                "model": self.model,
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "top_p": 0.9,
                    "top_k": 40
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/api/chat",
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("message", {}).get("content", "")
            else:
                logger.error(f"API error: {response.status_code} - {response.text}")
                return ""
                
        except Exception as e:
            logger.error(f"Error generating chat completion: {e}")
            return ""


class AIPlanner:
    """AI-powered query planner using function calling capabilities."""
    
    def __init__(self, ollama_client: OllamaClient):
        self.client = ollama_client
        self.system_prompt = self._build_system_prompt()
    
    def _build_system_prompt(self) -> str:
        """Build the system prompt for function calling."""
        return """You are an AI assistant that converts natural language queries into structured function call sequences.

Your task is to:
1. Analyze the user's query to understand their intent
2. Break down complex requests into discrete tasks
3. Map each task to appropriate functions from the available library
4. Return a JSON response with the execution plan

Available function categories:
- data_operations: retrieve, filter, sort, aggregate, transform data
- communication: send emails, SMS, notifications
- file_operations: read, write, copy, move files
- web_operations: HTTP requests, web scraping, downloads
- analytics: statistics, charts, reports, trends
- database: queries, CRUD operations, backups
- scheduling: calendar events, tasks, reminders
- financial: invoices, payments, reports, calculations
- system_operations: monitoring, logging, backups

Response format (JSON):
{
    "reasoning": "Explanation of how you broke down the query",
    "function_calls": [
        {
            "function_name": "function_name",
            "parameters": {"param1": "value1", "param2": "value2"},
            "output_variable": "variable_name",
            "depends_on": ["previous_variable"]
        }
    ]
}

Important guidelines:
- Use descriptive output variable names
- Ensure proper dependency ordering
- Include all necessary parameters
- Be specific with parameter values
- If information is missing, use reasonable defaults or ask for clarification"""
    
    def plan_execution(self, query: str, available_functions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Plan execution sequence for a user query."""
        
        # Build function reference for the prompt
        function_descriptions = []
        for func in available_functions:
            params = ", ".join([f"{p['name']}: {p['type']}" for p in func.get('parameters', [])])
            function_descriptions.append(f"- {func['name']}({params}): {func['description']}")
        
        functions_text = "\n".join(function_descriptions)
        
        prompt = f"""User Query: "{query}"

Available Functions:
{functions_text}

Please analyze this query and create an execution plan. Return only valid JSON."""
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": prompt}
        ]
        
        response = self.client.chat_completion(messages, temperature=0.1)
        
        try:
            # Try to parse JSON response
            if response.strip().startswith("```json"):
                response = response.strip()[7:-3]
            elif response.strip().startswith("```"):
                response = response.strip()[3:-3]
            
            plan = json.loads(response)
            
            # Validate the plan structure
            if "function_calls" not in plan:
                raise ValueError("Missing function_calls in response")
            
            return {
                "success": True,
                "plan": plan,
                "raw_response": response
            }
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Raw response: {response}")
            
            # Fallback: try to extract JSON from response
            try:
                start = response.find("{")
                end = response.rfind("}") + 1
                if start >= 0 and end > start:
                    json_part = response[start:end]
                    plan = json.loads(json_part)
                    return {
                        "success": True,
                        "plan": plan,
                        "raw_response": response
                    }
            except:
                pass
            
            return {
                "success": False,
                "error": f"Failed to parse AI response: {str(e)}",
                "raw_response": response
            }
        
        except Exception as e:
            logger.error(f"Error in plan execution: {e}")
            return {
                "success": False,
                "error": str(e),
                "raw_response": response
            }
