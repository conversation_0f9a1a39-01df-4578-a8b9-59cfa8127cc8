{"version": 3, "file": "streaming.mjs", "sourceRoot": "", "sources": ["../src/lib/streaming.ts"], "names": [], "mappings": "OAAO,EAAE,cAAc,EAAiB;OACjC,EAAE,SAAS,EAAE;OAEb,EAAE,QAAQ,EAAE;AAUnB,MAAM,OAAO,MAAM;IAGjB,YACU,QAAmC,EAC3C,UAA2B;QADnB,aAAQ,GAAR,QAAQ,CAA2B;QAG3C,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED,MAAM,CAAC,eAAe,CAAO,QAAkB,EAAE,UAA2B;QAC1E,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,MAAM,OAAO,GAAG,IAAI,UAAU,EAAE,CAAC;QAEjC,KAAK,SAAS,CAAC,CAAC,YAAY;YAC1B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;gBAClB,UAAU,CAAC,KAAK,EAAE,CAAC;gBACnB,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC,CAAC;aAC1E;YAED,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;YAEtC,MAAM,IAAI,GAAG,2BAA2B,CAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC/D,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,IAAI,EAAE;gBAC9B,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;oBAC5C,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;oBACjC,IAAI,GAAG;wBAAE,MAAM,GAAG,CAAC;iBACpB;aACF;YAED,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,KAAK,EAAE,EAAE;gBACtC,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACjC,IAAI,GAAG;oBAAE,MAAM,GAAG,CAAC;aACpB;QACH,CAAC;QAED,KAAK,SAAS,CAAC,CAAC,QAAQ;YACtB,IAAI,QAAQ,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;aAC7F;YACD,QAAQ,GAAG,IAAI,CAAC;YAChB,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI;gBACF,IAAI,KAAK,EAAE,MAAM,GAAG,IAAI,YAAY,EAAE,EAAE;oBACtC,IAAI,IAAI;wBAAE,SAAS;oBAEnB,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;wBACjC,IAAI,GAAG,IAAI,CAAC;wBACZ,SAAS;qBACV;oBAED,IAAI,GAAG,CAAC,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,KAAK,KAAK,OAAO,EAAE;wBAC/C,IAAI,IAAI,CAAC;wBAET,IAAI;4BACF,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;yBAC7B;wBAAC,OAAO,CAAC,EAAE;4BACV,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;4BAC9D,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;4BACtC,MAAM,CAAC,CAAC;yBACT;wBAED,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;4BACtB,MAAM,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;yBACvF;wBAED,MAAM,IAAI,CAAC;qBACZ;iBACF;gBACD,IAAI,GAAG,IAAI,CAAC;aACb;YAAC,OAAO,CAAC,EAAE;gBACV,kFAAkF;gBAClF,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,YAAY;oBAAE,OAAO;gBAC1D,MAAM,CAAC,CAAC;aACT;oBAAS;gBACR,mDAAmD;gBACnD,IAAI,CAAC,IAAI;oBAAE,UAAU,CAAC,KAAK,EAAE,CAAC;aAC/B;QACH,CAAC;QAED,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,kBAAkB,CAAO,cAA8B,EAAE,UAA2B;QACzF,IAAI,QAAQ,GAAG,KAAK,CAAC;QAErB,KAAK,SAAS,CAAC,CAAC,SAAS;YACvB,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAC;YAEtC,MAAM,IAAI,GAAG,2BAA2B,CAAQ,cAAc,CAAC,CAAC;YAChE,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,IAAI,EAAE;gBAC9B,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;oBAC5C,MAAM,IAAI,CAAC;iBACZ;aACF;YAED,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,KAAK,EAAE,EAAE;gBACtC,MAAM,IAAI,CAAC;aACZ;QACH,CAAC;QAED,KAAK,SAAS,CAAC,CAAC,QAAQ;YACtB,IAAI,QAAQ,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,0EAA0E,CAAC,CAAC;aAC7F;YACD,QAAQ,GAAG,IAAI,CAAC;YAChB,IAAI,IAAI,GAAG,KAAK,CAAC;YACjB,IAAI;gBACF,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,SAAS,EAAE,EAAE;oBACpC,IAAI,IAAI;wBAAE,SAAS;oBACnB,IAAI,IAAI;wBAAE,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;iBAClC;gBACD,IAAI,GAAG,IAAI,CAAC;aACb;YAAC,OAAO,CAAC,EAAE;gBACV,kFAAkF;gBAClF,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,CAAC,IAAI,KAAK,YAAY;oBAAE,OAAO;gBAC1D,MAAM,CAAC,CAAC;aACT;oBAAS;gBACR,mDAAmD;gBACnD,IAAI,CAAC,IAAI;oBAAE,UAAU,CAAC,KAAK,EAAE,CAAC;aAC/B;QACH,CAAC;QAED,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;IAC1C,CAAC;IAED,CAAC,MAAM,CAAC,aAAa,CAAC;QACpB,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,GAAG;QACD,MAAM,IAAI,GAAyC,EAAE,CAAC;QACtD,MAAM,KAAK,GAAyC,EAAE,CAAC;QACvD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAEjC,MAAM,WAAW,GAAG,CAAC,KAA2C,EAAuB,EAAE;YACvF,OAAO;gBACL,IAAI,EAAE,GAAG,EAAE;oBACT,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;wBACtB,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;wBAC/B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAClB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;qBACpB;oBACD,OAAO,KAAK,CAAC,KAAK,EAAG,CAAC;gBACxB,CAAC;aACF,CAAC;QACJ,CAAC,CAAC;QAEF,OAAO;YACL,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC;YACpD,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC;SACtD,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACH,gBAAgB;QACd,MAAM,IAAI,GAAG,IAAI,CAAC;QAClB,IAAI,IAAyB,CAAC;QAC9B,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;QAElC,OAAO,IAAI,cAAc,CAAC;YACxB,KAAK,CAAC,KAAK;gBACT,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;YACtC,CAAC;YACD,KAAK,CAAC,IAAI,CAAC,IAAI;gBACb,IAAI;oBACF,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;oBAC1C,IAAI,IAAI;wBAAE,OAAO,IAAI,CAAC,KAAK,EAAE,CAAC;oBAE9B,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC;oBAE3D,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;iBACrB;gBAAC,OAAO,GAAG,EAAE;oBACZ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;iBACjB;YACH,CAAC;YACD,KAAK,CAAC,MAAM;gBACV,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACxB,CAAC;SACF,CAAC,CAAC;IACL,CAAC;CACF;AAED,MAAM,UAAU;IAKd;QACE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;QACf,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;IACnB,CAAC;IAED,MAAM,CAAC,IAAY;QACjB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACvB,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;SAC3C;QAED,IAAI,CAAC,IAAI,EAAE;YACT,6DAA6D;YAC7D,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM;gBAAE,OAAO,IAAI,CAAC;YAElD,MAAM,GAAG,GAAoB;gBAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC1B,GAAG,EAAE,IAAI,CAAC,MAAM;aACjB,CAAC;YAEF,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;YAClB,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;YAEjB,OAAO,GAAG,CAAC;SACZ;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEvB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACxB,OAAO,IAAI,CAAC;SACb;QAED,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAEjD,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;YACzB,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;SAC5B;QAED,IAAI,SAAS,KAAK,OAAO,EAAE;YACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACpB;aAAM,IAAI,SAAS,KAAK,MAAM,EAAE;YAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACvB;QAED,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAED;;;;;GAKG;AACH,MAAM,WAAW;IASf;QACE,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC1B,CAAC;IAED,MAAM,CAAC,KAAY;QACjB,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAElC,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;YACnB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;SACzB;QACD,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;SAC1B;QAED,IAAI,CAAC,IAAI,EAAE;YACT,OAAO,EAAE,CAAC;SACX;QAED,MAAM,eAAe,GAAG,WAAW,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QACnF,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;QAEnD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE;YAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC;YAC5B,OAAO,EAAE,CAAC;SACX;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAC7D,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;SAClB;QAED,IAAI,CAAC,eAAe,EAAE;YACpB,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC;SACnC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,UAAU,CAAC,KAAY;QACrB,IAAI,KAAK,IAAI,IAAI;YAAE,OAAO,EAAE,CAAC;QAC7B,IAAI,OAAO,KAAK,KAAK,QAAQ;YAAE,OAAO,KAAK,CAAC;QAE5C,QAAQ;QACR,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;YACjC,IAAI,KAAK,YAAY,MAAM,EAAE;gBAC3B,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;aACzB;YACD,IAAI,KAAK,YAAY,UAAU,EAAE;gBAC/B,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;aACtC;YAED,MAAM,IAAI,SAAS,CACjB,wCAAwC,KAAK,CAAC,WAAW,CAAC,IAAI,mIAAmI,CAClM,CAAC;SACH;QAED,UAAU;QACV,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE;YACtC,IAAI,KAAK,YAAY,UAAU,IAAI,KAAK,YAAY,WAAW,EAAE;gBAC/D,IAAI,CAAC,WAAW,KAAhB,IAAI,CAAC,WAAW,GAAK,IAAI,WAAW,CAAC,MAAM,CAAC,EAAC;gBAC7C,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACvC;YAED,MAAM,IAAI,SAAS,CACjB,oDACG,KAAa,CAAC,WAAW,CAAC,IAC7B,gDAAgD,CACjD,CAAC;SACH;QAED,MAAM,IAAI,SAAS,CACjB,gGAAgG,CACjG,CAAC;IACJ,CAAC;IAED,KAAK;QACH,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAC3C,OAAO,EAAE,CAAC;SACX;QAED,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QACxB,OAAO,KAAK,CAAC;IACf,CAAC;;AA/FD,kBAAkB;AACX,yBAAa,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC1G,0BAAc,GAAG,kDAAkD,CAAC;AAgG7E,SAAS,SAAS,CAAC,GAAW,EAAE,SAAiB;IAC/C,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACrC,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,SAAS,EAAE,GAAG,CAAC,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;KACtF;IAED,OAAO,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACvB,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,2BAA2B,CAAI,MAAW;IACxD,IAAI,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;QAAE,OAAO,MAAM,CAAC;IAEhD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;IAClC,OAAO;QACL,KAAK,CAAC,IAAI;YACR,IAAI;gBACF,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;gBACnC,IAAI,MAAM,EAAE,IAAI;oBAAE,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,0CAA0C;gBAClF,OAAO,MAAM,CAAC;aACf;YAAC,OAAO,CAAC,EAAE;gBACV,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,2CAA2C;gBACjE,MAAM,CAAC,CAAC;aACT;QACH,CAAC;QACD,KAAK,CAAC,MAAM;YACV,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YACtC,MAAM,CAAC,WAAW,EAAE,CAAC;YACrB,MAAM,aAAa,CAAC;YACpB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;QAC1C,CAAC;QACD,CAAC,MAAM,CAAC,aAAa,CAAC;YACpB,OAAO,IAAI,CAAC;QACd,CAAC;KACF,CAAC;AACJ,CAAC"}