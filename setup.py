#!/usr/bin/env python3
"""
Complete setup script for the AI Function Calling Pipeline.
"""

import subprocess
import sys
import os
import time
from pathlib import Path


def run_command(command, check=True, capture_output=True):
    """Run a command and return the result."""
    print(f"Running: {command}")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            check=check, 
            capture_output=capture_output, 
            text=True
        )
        if result.stdout and capture_output:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return None


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True


def install_python_dependencies():
    """Install Python dependencies."""
    print("\n📦 Installing Python dependencies...")
    
    # Upgrade pip first
    run_command(f"{sys.executable} -m pip install --upgrade pip")
    
    # Install requirements
    result = run_command(f"{sys.executable} -m pip install -r requirements.txt")
    if result and result.returncode == 0:
        print("✅ Python dependencies installed successfully")
        return True
    else:
        print("❌ Failed to install Python dependencies")
        return False


def setup_ollama():
    """Set up Ollama and download the model."""
    print("\n🤖 Setting up Ollama...")
    
    # Run the Ollama setup script
    result = run_command(f"{sys.executable} setup_ollama.py")
    if result and result.returncode == 0:
        print("✅ Ollama setup completed")
        return True
    else:
        print("❌ Ollama setup failed")
        print("Please run 'python setup_ollama.py' manually")
        return False


def run_tests():
    """Run the test suite."""
    print("\n🧪 Running tests...")
    
    result = run_command(f"{sys.executable} -m pytest tests/ -v")
    if result and result.returncode == 0:
        print("✅ All tests passed")
        return True
    else:
        print("⚠️ Some tests failed (this might be expected if Ollama is not running)")
        return False


def start_pipeline():
    """Start the pipeline server."""
    print("\n🚀 Starting the pipeline...")
    print("The server will start at http://localhost:8000")
    print("Press Ctrl+C to stop the server")
    print()
    
    try:
        run_command(f"{sys.executable} -m src.main", capture_output=False)
    except KeyboardInterrupt:
        print("\n👋 Pipeline stopped")


def main():
    """Main setup function."""
    print("🤖 AI Function Calling Pipeline Setup")
    print("=====================================\n")
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install Python dependencies
    if not install_python_dependencies():
        print("\n❌ Setup failed at Python dependencies installation")
        sys.exit(1)
    
    # Setup Ollama
    print("\nWould you like to set up Ollama and download the Qwen 2.5 7B model?")
    print("This will download several GB of data and may take a while.")
    choice = input("Continue? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes']:
        if not setup_ollama():
            print("\n⚠️ Ollama setup failed, but you can continue without it")
            print("You can run 'python setup_ollama.py' later")
    else:
        print("⚠️ Skipping Ollama setup")
        print("Note: The pipeline requires Ollama to function properly")
    
    # Run tests
    print("\nWould you like to run the test suite?")
    choice = input("Run tests? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes']:
        run_tests()
    
    # Final setup complete
    print("\n🎉 Setup Complete!")
    print("\nNext steps:")
    print("1. Start the pipeline: python -m src.main")
    print("2. Open web interface: http://localhost:8000")
    print("3. Try the demo: python demo.py")
    print("4. Check API docs: http://localhost:8000/docs")
    
    # Ask if user wants to start now
    print("\nWould you like to start the pipeline now?")
    choice = input("Start pipeline? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes']:
        start_pipeline()
    else:
        print("\n👋 Setup complete! Run 'python -m src.main' when ready.")


if __name__ == "__main__":
    main()
