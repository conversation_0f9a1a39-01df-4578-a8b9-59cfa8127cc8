"""
Execution engine for running function call sequences.
"""

from typing import Dict, Any, List, Optional
from src.models.schemas import Execution<PERSON><PERSON>, FunctionCall, ExecutionResult, FunctionResult
from src.functions.registry import function_registry
import logging
import time
from datetime import datetime

logger = logging.getLogger(__name__)


class ExecutionEngine:
    """Engine for executing function call sequences."""
    
    def __init__(self):
        self.registry = function_registry
        self.execution_context = {}
    
    def execute_plan(self, plan: ExecutionPlan, dry_run: bool = False) -> ExecutionResult:
        """Execute a complete execution plan."""
        start_time = time.time()
        results = {}
        errors = []
        
        logger.info(f"Starting execution of plan with {len(plan.function_calls)} function calls")
        
        try:
            # Reset execution context
            self.execution_context = {}
            
            # Execute each function call in sequence
            for i, function_call in enumerate(plan.function_calls):
                logger.info(f"Executing step {i+1}: {function_call.function_name}")
                
                if dry_run:
                    result = self._simulate_function_call(function_call)
                else:
                    result = self._execute_function_call(function_call)
                
                # Store result in context if output variable is specified
                if function_call.output_variable:
                    self.execution_context[function_call.output_variable] = result.result
                
                # Store result for response
                results[f"step_{i+1}_{function_call.function_name}"] = {
                    "function_name": result.function_name,
                    "success": result.success,
                    "result": result.result,
                    "execution_time": result.execution_time
                }
                
                if result.error:
                    errors.append(f"Step {i+1} ({function_call.function_name}): {result.error}")
                
                # Stop execution if a critical function fails
                if not result.success and not dry_run:
                    logger.error(f"Function {function_call.function_name} failed, stopping execution")
                    break
            
            execution_time = time.time() - start_time
            success = len(errors) == 0
            
            return ExecutionResult(
                success=success,
                results=results,
                errors=errors,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Execution failed with error: {e}")
            
            return ExecutionResult(
                success=False,
                results=results,
                errors=[f"Execution engine error: {str(e)}"],
                execution_time=execution_time
            )
    
    def _execute_function_call(self, function_call: FunctionCall) -> FunctionResult:
        """Execute a single function call."""
        start_time = time.time()
        
        try:
            # Validate function exists
            if not self.registry._functions.get(function_call.function_name):
                return FunctionResult(
                    function_name=function_call.function_name,
                    success=False,
                    error=f"Function '{function_call.function_name}' not found",
                    execution_time=time.time() - start_time
                )
            
            # Resolve parameter dependencies
            resolved_parameters = self._resolve_parameters(function_call.parameters, function_call.depends_on)
            
            # Validate function call
            if not self.registry.validate_function_call(function_call.function_name, resolved_parameters):
                return FunctionResult(
                    function_name=function_call.function_name,
                    success=False,
                    error="Function call validation failed",
                    execution_time=time.time() - start_time
                )
            
            # Get function implementation
            func = self.registry.get_function(function_call.function_name)
            
            # Execute function
            result = func(**resolved_parameters)
            
            execution_time = time.time() - start_time
            
            return FunctionResult(
                function_name=function_call.function_name,
                success=True,
                result=result,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Error executing {function_call.function_name}: {e}")
            
            return FunctionResult(
                function_name=function_call.function_name,
                success=False,
                error=str(e),
                execution_time=execution_time
            )
    
    def _simulate_function_call(self, function_call: FunctionCall) -> FunctionResult:
        """Simulate a function call for dry run."""
        return FunctionResult(
            function_name=function_call.function_name,
            success=True,
            result=f"[DRY RUN] Would execute {function_call.function_name} with parameters: {function_call.parameters}",
            execution_time=0.1
        )
    
    def _resolve_parameters(self, parameters: Dict[str, Any], dependencies: List[str]) -> Dict[str, Any]:
        """Resolve parameter values from execution context."""
        resolved = parameters.copy()
        
        for param_name, param_value in parameters.items():
            # Check if parameter value references a variable from previous execution
            if isinstance(param_value, str) and param_value in self.execution_context:
                resolved[param_name] = self.execution_context[param_value]
            
            # Handle special variable references (e.g., ${variable_name})
            elif isinstance(param_value, str) and param_value.startswith("${") and param_value.endswith("}"):
                var_name = param_value[2:-1]
                if var_name in self.execution_context:
                    resolved[param_name] = self.execution_context[var_name]
                else:
                    logger.warning(f"Variable '{var_name}' not found in execution context")
        
        return resolved
    
    def get_available_functions(self) -> List[Dict[str, Any]]:
        """Get list of all available functions with their schemas."""
        functions = []
        for schema in self.registry.list_functions():
            functions.append({
                "name": schema.name,
                "description": schema.description,
                "category": schema.category,
                "parameters": [
                    {
                        "name": param.name,
                        "type": param.type.value,
                        "description": param.description,
                        "required": param.required,
                        "default": param.default,
                        "enum_values": param.enum_values
                    }
                    for param in schema.parameters
                ],
                "returns": schema.returns,
                "examples": schema.examples
            })
        return functions
    
    def validate_execution_plan(self, plan: ExecutionPlan) -> Dict[str, Any]:
        """Validate an execution plan before execution."""
        errors = []
        warnings = []
        
        # Check if all functions exist
        for function_call in plan.function_calls:
            if function_call.function_name not in self.registry._functions:
                errors.append(f"Function '{function_call.function_name}' does not exist")
        
        # Check parameter dependencies
        available_variables = set()
        for function_call in plan.function_calls:
            # Check if dependencies are available
            for dep in function_call.depends_on:
                if dep not in available_variables:
                    warnings.append(f"Dependency '{dep}' for {function_call.function_name} may not be available")
            
            # Add output variable to available set
            if function_call.output_variable:
                available_variables.add(function_call.output_variable)
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
