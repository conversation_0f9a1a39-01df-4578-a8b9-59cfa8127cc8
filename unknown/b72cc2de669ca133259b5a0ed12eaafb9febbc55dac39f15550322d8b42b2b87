/**
 * @license
 * web-streams-polyfill v4.0.0-beta.3
 * Copyright 2021 <PERSON><PERSON>, <PERSON><PERSON><PERSON> and other contributors.
 * This code is released under the MIT license.
 * SPDX-License-Identifier: MIT
 */
var e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol:function(e){return"Symbol(".concat(e,")")};function r(){}function t(e){return"object"==typeof e&&null!==e||"function"==typeof e}var n=r;function o(e,r){try{Object.defineProperty(e,"name",{value:r,configurable:!0})}catch(e){}}var a=Promise,i=Promise.prototype.then,l=Promise.resolve.bind(a),u=Promise.reject.bind(a);function s(e){return new a(e)}function c(e){return l(e)}function d(e){return u(e)}function f(e,r,t){return i.call(e,r,t)}function b(e,r,t){f(f(e,r,t),void 0,n)}function p(e,r){b(e,r)}function h(e,r){b(e,void 0,r)}function _(e,r,t){return f(e,r,t)}function y(e){f(e,void 0,n)}var v=function(e){if("function"==typeof queueMicrotask)v=queueMicrotask;else{var r=c(void 0);v=function(e){return f(r,e)}}return v(e)};function m(e,r,t){if("function"!=typeof e)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(e,r,t)}function g(e,r,t){try{return c(m(e,r,t))}catch(e){return d(e)}}var w=function(){function e(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}return Object.defineProperty(e.prototype,"length",{get:function(){return this._size},enumerable:!1,configurable:!0}),e.prototype.push=function(e){var r=this._back,t=r;16383===r._elements.length&&(t={_elements:[],_next:void 0}),r._elements.push(e),t!==r&&(this._back=t,r._next=t),++this._size},e.prototype.shift=function(){var e=this._front,r=e,t=this._cursor,n=t+1,o=e._elements,a=o[t];return 16384===n&&(r=e._next,n=0),--this._size,this._cursor=n,e!==r&&(this._front=r),o[t]=void 0,a},e.prototype.forEach=function(e){for(var r=this._cursor,t=this._front,n=t._elements;!(r===n.length&&void 0===t._next||r===n.length&&(r=0,0===(n=(t=t._next)._elements).length));)e(n[r]),++r},e.prototype.peek=function(){var e=this._front,r=this._cursor;return e._elements[r]},e}(),S=e("[[AbortSteps]]"),R=e("[[ErrorSteps]]"),T=e("[[CancelSteps]]"),q=e("[[PullSteps]]"),P=e("[[ReleaseSteps]]");function C(e,r){e._ownerReadableStream=r,r._reader=e,"readable"===r._state?k(e):"closed"===r._state?function(e){k(e),A(e)}(e):j(e,r._storedError)}function E(e,r){return at(e._ownerReadableStream,r)}function O(e){var r=e._ownerReadableStream;"readable"===r._state?B(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(e,r){j(e,r)}(e,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")),r._readableStreamController[P](),r._reader=void 0,e._ownerReadableStream=void 0}function W(e){return new TypeError("Cannot "+e+" a stream using a released reader")}function k(e){e._closedPromise=s((function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t}))}function j(e,r){k(e),B(e,r)}function B(e,r){void 0!==e._closedPromise_reject&&(y(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function A(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}var z=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},L=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function F(e,r){if(void 0!==e&&("object"!=typeof(t=e)&&"function"!=typeof t))throw new TypeError("".concat(r," is not an object."));var t}function I(e,r){if("function"!=typeof e)throw new TypeError("".concat(r," is not a function."))}function D(e,r){if(!function(e){return"object"==typeof e&&null!==e||"function"==typeof e}(e))throw new TypeError("".concat(r," is not an object."))}function M(e,r,t){if(void 0===e)throw new TypeError("Parameter ".concat(r," is required in '").concat(t,"'."))}function Q(e,r,t){if(void 0===e)throw new TypeError("".concat(r," is required in '").concat(t,"'."))}function Y(e){return Number(e)}function N(e){return 0===e?0:e}function H(e,r){var t=Number.MAX_SAFE_INTEGER,n=Number(e);if(n=N(n),!z(n))throw new TypeError("".concat(r," is not a finite number"));if((n=function(e){return N(L(e))}(n))<0||n>t)throw new TypeError("".concat(r," is outside the accepted range of ").concat(0," to ").concat(t,", inclusive"));return z(n)&&0!==n?n:0}function x(e){if(!t(e))return!1;if("function"!=typeof e.getReader)return!1;try{return"boolean"==typeof e.locked}catch(e){return!1}}function V(e){if(!t(e))return!1;if("function"!=typeof e.getWriter)return!1;try{return"boolean"==typeof e.locked}catch(e){return!1}}function U(e,r){if(!nt(e))throw new TypeError("".concat(r," is not a ReadableStream."))}function G(e,r){e._reader._readRequests.push(r)}function X(e,r,t){var n=e._reader._readRequests.shift();t?n._closeSteps():n._chunkSteps(r)}function J(e){return e._reader._readRequests.length}function K(e){var r=e._reader;return void 0!==r&&!!$(r)}var Z=function(){function ReadableStreamDefaultReader(e){if(M(e,1,"ReadableStreamDefaultReader"),U(e,"First parameter"),ot(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");C(this,e),this._readRequests=new w}return Object.defineProperty(ReadableStreamDefaultReader.prototype,"closed",{get:function(){return $(this)?this._closedPromise:d(re("closed"))},enumerable:!1,configurable:!0}),ReadableStreamDefaultReader.prototype.cancel=function(e){return void 0===e&&(e=void 0),$(this)?void 0===this._ownerReadableStream?d(W("cancel")):E(this,e):d(re("cancel"))},ReadableStreamDefaultReader.prototype.read=function(){if(!$(this))return d(re("read"));if(void 0===this._ownerReadableStream)return d(W("read from"));var e,r,t=s((function(t,n){e=t,r=n}));return function(e,r){var t=e._ownerReadableStream;t._disturbed=!0,"closed"===t._state?r._closeSteps():"errored"===t._state?r._errorSteps(t._storedError):t._readableStreamController[q](r)}(this,{_chunkSteps:function(r){return e({value:r,done:!1})},_closeSteps:function(){return e({value:void 0,done:!0})},_errorSteps:function(e){return r(e)}}),t},ReadableStreamDefaultReader.prototype.releaseLock=function(){if(!$(this))throw re("releaseLock");void 0!==this._ownerReadableStream&&function(e){O(e);var r=new TypeError("Reader was released");ee(e,r)}(this)},ReadableStreamDefaultReader}();function $(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof Z)}function ee(e,r){var t=e._readRequests;e._readRequests=new w,t.forEach((function(e){e._errorSteps(r)}))}function re(e){return new TypeError("ReadableStreamDefaultReader.prototype.".concat(e," can only be used on a ReadableStreamDefaultReader"))}Object.defineProperties(Z.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),o(Z.prototype.cancel,"cancel"),o(Z.prototype.read,"read"),o(Z.prototype.releaseLock,"releaseLock"),"symbol"==typeof e.toStringTag&&Object.defineProperty(Z.prototype,e.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});var te=function(){function e(e,r){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=r}return e.prototype.next=function(){var e=this,r=function(){return e._nextSteps()};return this._ongoingPromise=this._ongoingPromise?_(this._ongoingPromise,r,r):r(),this._ongoingPromise},e.prototype.return=function(e){var r=this,t=function(){return r._returnSteps(e)};return this._ongoingPromise?_(this._ongoingPromise,t,t):t()},e.prototype._nextSteps=function(){var e=this;if(this._isFinished)return Promise.resolve({value:void 0,done:!0});var r=this._reader;return void 0===r?d(W("iterate")):f(r.read(),(function(r){var t;return e._ongoingPromise=void 0,r.done&&(e._isFinished=!0,null===(t=e._reader)||void 0===t||t.releaseLock(),e._reader=void 0),r}),(function(r){var t;throw e._ongoingPromise=void 0,e._isFinished=!0,null===(t=e._reader)||void 0===t||t.releaseLock(),e._reader=void 0,r}))},e.prototype._returnSteps=function(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;var r=this._reader;if(void 0===r)return d(W("finish iterating"));if(this._reader=void 0,!this._preventCancel){var t=r.cancel(e);return r.releaseLock(),_(t,(function(){return{value:e,done:!0}}))}return r.releaseLock(),c({value:e,done:!0})},e}(),ne={next:function(){return oe(this)?this._asyncIteratorImpl.next():d(ae("next"))},return:function(e){return oe(this)?this._asyncIteratorImpl.return(e):d(ae("return"))}};function oe(e){if(!t(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof te}catch(e){return!1}}function ae(e){return new TypeError("ReadableStreamAsyncIterator.".concat(e," can only be used on a ReadableSteamAsyncIterator"))}"symbol"==typeof e.asyncIterator&&Object.defineProperty(ne,e.asyncIterator,{value:function(){return this},writable:!0,configurable:!0});var ie=Number.isNaN||function(e){return e!=e};function le(e,r,t,n,o){new Uint8Array(e).set(new Uint8Array(t,n,o),r)}function ue(e){var r=function(e,r,t){if(e.slice)return e.slice(r,t);var n=t-r,o=new ArrayBuffer(n);return le(o,0,e,r,n),o}(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(r)}function se(e){var r=e._queue.shift();return e._queueTotalSize-=r.size,e._queueTotalSize<0&&(e._queueTotalSize=0),r.value}function ce(e,r,t){if("number"!=typeof(n=t)||ie(n)||n<0||t===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");var n;e._queue.push({value:r,size:t}),e._queueTotalSize+=t}function de(e){e._queue=new w,e._queueTotalSize=0}var fe=function(){function ReadableStreamBYOBRequest(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableStreamBYOBRequest.prototype,"view",{get:function(){if(!he(this))throw ze("view");return this._view},enumerable:!1,configurable:!0}),ReadableStreamBYOBRequest.prototype.respond=function(e){if(!he(this))throw ze("respond");if(M(e,1,"respond"),e=H(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");this._view.buffer,function(e,r){var t=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==r)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===r)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(t.bytesFilled+r>t.byteLength)throw new RangeError("bytesWritten out of range")}t.buffer=t.buffer,Ee(e,r)}(this._associatedReadableByteStreamController,e)},ReadableStreamBYOBRequest.prototype.respondWithNewView=function(e){if(!he(this))throw ze("respondWithNewView");if(M(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw new TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");e.buffer,function(e,r){var t=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==r.byteLength)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===r.byteLength)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(t.byteOffset+t.bytesFilled!==r.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(t.bufferByteLength!==r.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(t.bytesFilled+r.byteLength>t.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");var n=r.byteLength;t.buffer=r.buffer,Ee(e,n)}(this._associatedReadableByteStreamController,e)},ReadableStreamBYOBRequest}();Object.defineProperties(fe.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),o(fe.prototype.respond,"respond"),o(fe.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof e.toStringTag&&Object.defineProperty(fe.prototype,e.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});var be=function(){function ReadableByteStreamController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableByteStreamController.prototype,"byobRequest",{get:function(){if(!pe(this))throw Le("byobRequest");return function(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){var r=e._pendingPullIntos.peek(),t=new Uint8Array(r.buffer,r.byteOffset+r.bytesFilled,r.byteLength-r.bytesFilled),n=Object.create(fe.prototype);!function(e,r,t){e._associatedReadableByteStreamController=r,e._view=t}(n,e,t),e._byobRequest=n}return e._byobRequest}(this)},enumerable:!1,configurable:!0}),Object.defineProperty(ReadableByteStreamController.prototype,"desiredSize",{get:function(){if(!pe(this))throw Le("desiredSize");return Be(this)},enumerable:!1,configurable:!0}),ReadableByteStreamController.prototype.close=function(){if(!pe(this))throw Le("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");var e=this._controlledReadableByteStream._state;if("readable"!==e)throw new TypeError("The stream (in ".concat(e," state) is not in the readable state and cannot be closed"));!function(e){var r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;if(e._queueTotalSize>0)return void(e._closeRequested=!0);if(e._pendingPullIntos.length>0){if(e._pendingPullIntos.peek().bytesFilled>0){var t=new TypeError("Insufficient bytes to fill elements in the given buffer");throw ke(e,t),t}}We(e),it(r)}(this)},ReadableByteStreamController.prototype.enqueue=function(e){if(!pe(this))throw Le("enqueue");if(M(e,1,"enqueue"),!ArrayBuffer.isView(e))throw new TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw new TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");var r=this._controlledReadableByteStream._state;if("readable"!==r)throw new TypeError("The stream (in ".concat(r," state) is not in the readable state and cannot be enqueued to"));!function(e,r){var t=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==t._state)return;var n=r.buffer,o=r.byteOffset,a=r.byteLength,i=n;if(e._pendingPullIntos.length>0){var l=e._pendingPullIntos.peek();l.buffer,0,Pe(e),l.buffer=l.buffer,"none"===l.readerType&&Se(e,l)}if(K(t)){if(function(e){var r=e._controlledReadableByteStream._reader;for(;r._readRequests.length>0;){if(0===e._queueTotalSize)return;je(e,r._readRequests.shift())}}(e),0===J(t))ge(e,i,o,a);else e._pendingPullIntos.length>0&&Oe(e),X(t,new Uint8Array(i,o,a),!1)}else De(t)?(ge(e,i,o,a),Ce(e)):ge(e,i,o,a);_e(e)}(this,e)},ReadableByteStreamController.prototype.error=function(e){if(void 0===e&&(e=void 0),!pe(this))throw Le("error");ke(this,e)},ReadableByteStreamController.prototype[T]=function(e){ye(this),de(this);var r=this._cancelAlgorithm(e);return We(this),r},ReadableByteStreamController.prototype[q]=function(e){var r=this._controlledReadableByteStream;if(this._queueTotalSize>0)je(this,e);else{var t=this._autoAllocateChunkSize;if(void 0!==t){var n=void 0;try{n=new ArrayBuffer(t)}catch(r){return void e._errorSteps(r)}var o={buffer:n,bufferByteLength:t,byteOffset:0,byteLength:t,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(o)}G(r,e),_e(this)}},ReadableByteStreamController.prototype[P]=function(){if(this._pendingPullIntos.length>0){var e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new w,this._pendingPullIntos.push(e)}},ReadableByteStreamController}();function pe(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof be)}function he(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof fe)}function _e(e){var r=function(e){var r=e._controlledReadableByteStream;if("readable"!==r._state)return!1;if(e._closeRequested)return!1;if(!e._started)return!1;if(K(r)&&J(r)>0)return!0;if(De(r)&&Ie(r)>0)return!0;if(Be(e)>0)return!0;return!1}(e);r&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,b(e._pullAlgorithm(),(function(){return e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,_e(e)),null}),(function(r){return ke(e,r),null}))))}function ye(e){Pe(e),e._pendingPullIntos=new w}function ve(e,r){var t=!1;"closed"===e._state&&(t=!0);var n=me(r);"default"===r.readerType?X(e,n,t):function(e,r,t){var n=e._reader._readIntoRequests.shift();t?n._closeSteps(r):n._chunkSteps(r)}(e,n,t)}function me(e){var r=e.bytesFilled,t=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,r/t)}function ge(e,r,t,n){e._queue.push({buffer:r,byteOffset:t,byteLength:n}),e._queueTotalSize+=n}function we(e,r,t,n){var o;try{o=r.slice(t,t+n)}catch(r){throw ke(e,r),r}ge(e,o,0,n)}function Se(e,r){r.bytesFilled>0&&we(e,r.buffer,r.byteOffset,r.bytesFilled),Oe(e)}function Re(e,r){var t=r.elementSize,n=r.bytesFilled-r.bytesFilled%t,o=Math.min(e._queueTotalSize,r.byteLength-r.bytesFilled),a=r.bytesFilled+o,i=a-a%t,l=o,u=!1;i>n&&(l=i-r.bytesFilled,u=!0);for(var s=e._queue;l>0;){var c=s.peek(),d=Math.min(l,c.byteLength),f=r.byteOffset+r.bytesFilled;le(r.buffer,f,c.buffer,c.byteOffset,d),c.byteLength===d?s.shift():(c.byteOffset+=d,c.byteLength-=d),e._queueTotalSize-=d,Te(e,d,r),l-=d}return u}function Te(e,r,t){t.bytesFilled+=r}function qe(e){0===e._queueTotalSize&&e._closeRequested?(We(e),it(e._controlledReadableByteStream)):_e(e)}function Pe(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function Ce(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;var r=e._pendingPullIntos.peek();Re(e,r)&&(Oe(e),ve(e._controlledReadableByteStream,r))}}function Ee(e,r){var t=e._pendingPullIntos.peek();Pe(e),"closed"===e._controlledReadableByteStream._state?function(e,r){"none"===r.readerType&&Oe(e);var t=e._controlledReadableByteStream;if(De(t))for(;Ie(t)>0;)ve(t,Oe(e))}(e,t):function(e,r,t){if(Te(0,r,t),"none"===t.readerType)return Se(e,t),void Ce(e);if(!(t.bytesFilled<t.elementSize)){Oe(e);var n=t.bytesFilled%t.elementSize;if(n>0){var o=t.byteOffset+t.bytesFilled;we(e,t.buffer,o-n,n)}t.bytesFilled-=n,ve(e._controlledReadableByteStream,t),Ce(e)}}(e,r,t),_e(e)}function Oe(e){return e._pendingPullIntos.shift()}function We(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function ke(e,r){var t=e._controlledReadableByteStream;"readable"===t._state&&(ye(e),de(e),We(e),lt(t,r))}function je(e,r){var t=e._queue.shift();e._queueTotalSize-=t.byteLength,qe(e);var n=new Uint8Array(t.buffer,t.byteOffset,t.byteLength);r._chunkSteps(n)}function Be(e){var r=e._controlledReadableByteStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function Ae(e,r,t){var n,o,a,i=Object.create(be.prototype);n=void 0!==r.start?function(){return r.start(i)}:function(){},o=void 0!==r.pull?function(){return r.pull(i)}:function(){return c(void 0)},a=void 0!==r.cancel?function(e){return r.cancel(e)}:function(){return c(void 0)};var l=r.autoAllocateChunkSize;if(0===l)throw new TypeError("autoAllocateChunkSize must be greater than 0");!function(e,r,t,n,o,a,i){r._controlledReadableByteStream=e,r._pullAgain=!1,r._pulling=!1,r._byobRequest=null,r._queue=r._queueTotalSize=void 0,de(r),r._closeRequested=!1,r._started=!1,r._strategyHWM=a,r._pullAlgorithm=n,r._cancelAlgorithm=o,r._autoAllocateChunkSize=i,r._pendingPullIntos=new w,e._readableStreamController=r,b(c(t()),(function(){return r._started=!0,_e(r),null}),(function(e){return ke(r,e),null}))}(e,i,n,o,a,t,l)}function ze(e){return new TypeError("ReadableStreamBYOBRequest.prototype.".concat(e," can only be used on a ReadableStreamBYOBRequest"))}function Le(e){return new TypeError("ReadableByteStreamController.prototype.".concat(e," can only be used on a ReadableByteStreamController"))}function Fe(e,r){e._reader._readIntoRequests.push(r)}function Ie(e){return e._reader._readIntoRequests.length}function De(e){var r=e._reader;return void 0!==r&&!!Qe(r)}Object.defineProperties(be.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),o(be.prototype.close,"close"),o(be.prototype.enqueue,"enqueue"),o(be.prototype.error,"error"),"symbol"==typeof e.toStringTag&&Object.defineProperty(be.prototype,e.toStringTag,{value:"ReadableByteStreamController",configurable:!0});var Me=function(){function ReadableStreamBYOBReader(e){if(M(e,1,"ReadableStreamBYOBReader"),U(e,"First parameter"),ot(e))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!pe(e._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");C(this,e),this._readIntoRequests=new w}return Object.defineProperty(ReadableStreamBYOBReader.prototype,"closed",{get:function(){return Qe(this)?this._closedPromise:d(Ne("closed"))},enumerable:!1,configurable:!0}),ReadableStreamBYOBReader.prototype.cancel=function(e){return void 0===e&&(e=void 0),Qe(this)?void 0===this._ownerReadableStream?d(W("cancel")):E(this,e):d(Ne("cancel"))},ReadableStreamBYOBReader.prototype.read=function(e){if(!Qe(this))return d(Ne("read"));if(!ArrayBuffer.isView(e))return d(new TypeError("view must be an array buffer view"));if(0===e.byteLength)return d(new TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return d(new TypeError("view's buffer must have non-zero byteLength"));if(e.buffer,void 0===this._ownerReadableStream)return d(W("read from"));var r,t,n=s((function(e,n){r=e,t=n}));return function(e,r,t){var n=e._ownerReadableStream;n._disturbed=!0,"errored"===n._state?t._errorSteps(n._storedError):function(e,r,t){var n=e._controlledReadableByteStream,o=1;r.constructor!==DataView&&(o=r.constructor.BYTES_PER_ELEMENT);var a=r.constructor,i=r.buffer,l={buffer:i,bufferByteLength:i.byteLength,byteOffset:r.byteOffset,byteLength:r.byteLength,bytesFilled:0,elementSize:o,viewConstructor:a,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(l),void Fe(n,t);if("closed"!==n._state){if(e._queueTotalSize>0){if(Re(e,l)){var u=me(l);return qe(e),void t._chunkSteps(u)}if(e._closeRequested){var s=new TypeError("Insufficient bytes to fill elements in the given buffer");return ke(e,s),void t._errorSteps(s)}}e._pendingPullIntos.push(l),Fe(n,t),_e(e)}else{var c=new a(l.buffer,l.byteOffset,0);t._closeSteps(c)}}(n._readableStreamController,r,t)}(this,e,{_chunkSteps:function(e){return r({value:e,done:!1})},_closeSteps:function(e){return r({value:e,done:!0})},_errorSteps:function(e){return t(e)}}),n},ReadableStreamBYOBReader.prototype.releaseLock=function(){if(!Qe(this))throw Ne("releaseLock");void 0!==this._ownerReadableStream&&function(e){O(e);var r=new TypeError("Reader was released");Ye(e,r)}(this)},ReadableStreamBYOBReader}();function Qe(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof Me)}function Ye(e,r){var t=e._readIntoRequests;e._readIntoRequests=new w,t.forEach((function(e){e._errorSteps(r)}))}function Ne(e){return new TypeError("ReadableStreamBYOBReader.prototype.".concat(e," can only be used on a ReadableStreamBYOBReader"))}function He(e,r){var t=e.highWaterMark;if(void 0===t)return r;if(ie(t)||t<0)throw new RangeError("Invalid highWaterMark");return t}function xe(e){var r=e.size;return r||function(){return 1}}function Ve(e,r){F(e,r);var t=null==e?void 0:e.highWaterMark,n=null==e?void 0:e.size;return{highWaterMark:void 0===t?void 0:Y(t),size:void 0===n?void 0:Ue(n,"".concat(r," has member 'size' that"))}}function Ue(e,r){return I(e,r),function(r){return Y(e(r))}}function Ge(e,r,t){return I(e,t),function(t){return g(e,r,[t])}}function Xe(e,r,t){return I(e,t),function(){return g(e,r,[])}}function Je(e,r,t){return I(e,t),function(t){return m(e,r,[t])}}function Ke(e,r,t){return I(e,t),function(t,n){return g(e,r,[t,n])}}Object.defineProperties(Me.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),o(Me.prototype.cancel,"cancel"),o(Me.prototype.read,"read"),o(Me.prototype.releaseLock,"releaseLock"),"symbol"==typeof e.toStringTag&&Object.defineProperty(Me.prototype,e.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});var Ze="function"==typeof AbortController;var $e=function(){function WritableStream(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:D(e,"First parameter");var t,n=Ve(r,"Second parameter"),o=function(e,r){F(e,r);var t=null==e?void 0:e.abort,n=null==e?void 0:e.close,o=null==e?void 0:e.start,a=null==e?void 0:e.type,i=null==e?void 0:e.write;return{abort:void 0===t?void 0:Ge(t,e,"".concat(r," has member 'abort' that")),close:void 0===n?void 0:Xe(n,e,"".concat(r," has member 'close' that")),start:void 0===o?void 0:Je(o,e,"".concat(r," has member 'start' that")),write:void 0===i?void 0:Ke(i,e,"".concat(r," has member 'write' that")),type:a}}(e,"First parameter");if((t=this)._state="writable",t._storedError=void 0,t._writer=void 0,t._writableStreamController=void 0,t._writeRequests=new w,t._inFlightWriteRequest=void 0,t._closeRequest=void 0,t._inFlightCloseRequest=void 0,t._pendingAbortRequest=void 0,t._backpressure=!1,void 0!==o.type)throw new RangeError("Invalid type is specified");var a=xe(n);!function(e,r,t,n){var o,a,i,l,u=Object.create(pr.prototype);o=void 0!==r.start?function(){return r.start(u)}:function(){};a=void 0!==r.write?function(e){return r.write(e,u)}:function(){return c(void 0)};i=void 0!==r.close?function(){return r.close()}:function(){return c(void 0)};l=void 0!==r.abort?function(e){return r.abort(e)}:function(){return c(void 0)};!function(e,r,t,n,o,a,i,l){r._controlledWritableStream=e,e._writableStreamController=r,r._queue=void 0,r._queueTotalSize=void 0,de(r),r._abortReason=void 0,r._abortController=function(){if(Ze)return new AbortController}(),r._started=!1,r._strategySizeAlgorithm=l,r._strategyHWM=i,r._writeAlgorithm=n,r._closeAlgorithm=o,r._abortAlgorithm=a;var u=gr(r);sr(e,u),b(c(t()),(function(){return r._started=!0,vr(r),null}),(function(t){return r._started=!0,or(e,t),null}))}(e,u,o,a,i,l,t,n)}(this,o,He(n,1),a)}return Object.defineProperty(WritableStream.prototype,"locked",{get:function(){if(!er(this))throw Sr("locked");return rr(this)},enumerable:!1,configurable:!0}),WritableStream.prototype.abort=function(e){return void 0===e&&(e=void 0),er(this)?rr(this)?d(new TypeError("Cannot abort a stream that already has a writer")):tr(this,e):d(Sr("abort"))},WritableStream.prototype.close=function(){return er(this)?rr(this)?d(new TypeError("Cannot close a stream that already has a writer")):lr(this)?d(new TypeError("Cannot close an already-closing stream")):nr(this):d(Sr("close"))},WritableStream.prototype.getWriter=function(){if(!er(this))throw Sr("getWriter");return new cr(this)},WritableStream}();function er(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof $e)}function rr(e){return void 0!==e._writer}function tr(e,r){var t;if("closed"===e._state||"errored"===e._state)return c(void 0);e._writableStreamController._abortReason=r,null===(t=e._writableStreamController._abortController)||void 0===t||t.abort(r);var n=e._state;if("closed"===n||"errored"===n)return c(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;var o=!1;"erroring"===n&&(o=!0,r=void 0);var a=s((function(t,n){e._pendingAbortRequest={_promise:void 0,_resolve:t,_reject:n,_reason:r,_wasAlreadyErroring:o}}));return e._pendingAbortRequest._promise=a,o||ar(e,r),a}function nr(e){var r=e._state;if("closed"===r||"errored"===r)return d(new TypeError("The stream (in ".concat(r," state) is not in the writable state and cannot be closed")));var t,n=s((function(r,t){var n={_resolve:r,_reject:t};e._closeRequest=n})),o=e._writer;return void 0!==o&&e._backpressure&&"writable"===r&&Ar(o),ce(t=e._writableStreamController,br,0),vr(t),n}function or(e,r){"writable"!==e._state?ir(e):ar(e,r)}function ar(e,r){var t=e._writableStreamController;e._state="erroring",e._storedError=r;var n=e._writer;void 0!==n&&fr(n,r),!function(e){if(void 0===e._inFlightWriteRequest&&void 0===e._inFlightCloseRequest)return!1;return!0}(e)&&t._started&&ir(e)}function ir(e){e._state="errored",e._writableStreamController[R]();var r=e._storedError;if(e._writeRequests.forEach((function(e){e._reject(r)})),e._writeRequests=new w,void 0!==e._pendingAbortRequest){var t=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,t._wasAlreadyErroring)return t._reject(r),void ur(e);b(e._writableStreamController[S](t._reason),(function(){return t._resolve(),ur(e),null}),(function(r){return t._reject(r),ur(e),null}))}else ur(e)}function lr(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function ur(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);var r=e._writer;void 0!==r&&Er(r,e._storedError)}function sr(e,r){var t=e._writer;void 0!==t&&r!==e._backpressure&&(r?function(e){Wr(e)}(t):Ar(t)),e._backpressure=r}Object.defineProperties($e.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),o($e.prototype.abort,"abort"),o($e.prototype.close,"close"),o($e.prototype.getWriter,"getWriter"),"symbol"==typeof e.toStringTag&&Object.defineProperty($e.prototype,e.toStringTag,{value:"WritableStream",configurable:!0});var cr=function(){function WritableStreamDefaultWriter(e){if(M(e,1,"WritableStreamDefaultWriter"),function(e,r){if(!er(e))throw new TypeError("".concat(r," is not a WritableStream."))}(e,"First parameter"),rr(e))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;var r,t=e._state;if("writable"===t)!lr(e)&&e._backpressure?Wr(this):jr(this),Pr(this);else if("erroring"===t)kr(this,e._storedError),Pr(this);else if("closed"===t)jr(this),Pr(r=this),Or(r);else{var n=e._storedError;kr(this,n),Cr(this,n)}}return Object.defineProperty(WritableStreamDefaultWriter.prototype,"closed",{get:function(){return dr(this)?this._closedPromise:d(Tr("closed"))},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultWriter.prototype,"desiredSize",{get:function(){if(!dr(this))throw Tr("desiredSize");if(void 0===this._ownerWritableStream)throw qr("desiredSize");return function(e){var r=e._ownerWritableStream,t=r._state;if("errored"===t||"erroring"===t)return null;if("closed"===t)return 0;return yr(r._writableStreamController)}(this)},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultWriter.prototype,"ready",{get:function(){return dr(this)?this._readyPromise:d(Tr("ready"))},enumerable:!1,configurable:!0}),WritableStreamDefaultWriter.prototype.abort=function(e){return void 0===e&&(e=void 0),dr(this)?void 0===this._ownerWritableStream?d(qr("abort")):function(e,r){return tr(e._ownerWritableStream,r)}(this,e):d(Tr("abort"))},WritableStreamDefaultWriter.prototype.close=function(){if(!dr(this))return d(Tr("close"));var e=this._ownerWritableStream;return void 0===e?d(qr("close")):lr(e)?d(new TypeError("Cannot close an already-closing stream")):nr(this._ownerWritableStream)},WritableStreamDefaultWriter.prototype.releaseLock=function(){if(!dr(this))throw Tr("releaseLock");void 0!==this._ownerWritableStream&&function(e){var r=e._ownerWritableStream,t=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");fr(e,t),function(e,r){"pending"===e._closedPromiseState?Er(e,r):function(e,r){Cr(e,r)}(e,r)}(e,t),r._writer=void 0,e._ownerWritableStream=void 0}(this)},WritableStreamDefaultWriter.prototype.write=function(e){return void 0===e&&(e=void 0),dr(this)?void 0===this._ownerWritableStream?d(qr("write to")):function(e,r){var t=e._ownerWritableStream,n=t._writableStreamController,o=function(e,r){try{return e._strategySizeAlgorithm(r)}catch(r){return mr(e,r),1}}(n,r);if(t!==e._ownerWritableStream)return d(qr("write to"));var a=t._state;if("errored"===a)return d(t._storedError);if(lr(t)||"closed"===a)return d(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===a)return d(t._storedError);var i=function(e){return s((function(r,t){var n={_resolve:r,_reject:t};e._writeRequests.push(n)}))}(t);return function(e,r,t){try{ce(e,r,t)}catch(r){return void mr(e,r)}var n=e._controlledWritableStream;if(!lr(n)&&"writable"===n._state){sr(n,gr(e))}vr(e)}(n,r,o),i}(this,e):d(Tr("write"))},WritableStreamDefaultWriter}();function dr(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof cr)}function fr(e,r){"pending"===e._readyPromiseState?Br(e,r):function(e,r){kr(e,r)}(e,r)}Object.defineProperties(cr.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),o(cr.prototype.abort,"abort"),o(cr.prototype.close,"close"),o(cr.prototype.releaseLock,"releaseLock"),o(cr.prototype.write,"write"),"symbol"==typeof e.toStringTag&&Object.defineProperty(cr.prototype,e.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});var br={},pr=function(){function WritableStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(WritableStreamDefaultController.prototype,"abortReason",{get:function(){if(!hr(this))throw Rr("abortReason");return this._abortReason},enumerable:!1,configurable:!0}),Object.defineProperty(WritableStreamDefaultController.prototype,"signal",{get:function(){if(!hr(this))throw Rr("signal");if(void 0===this._abortController)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal},enumerable:!1,configurable:!0}),WritableStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!hr(this))throw Rr("error");"writable"===this._controlledWritableStream._state&&wr(this,e)},WritableStreamDefaultController.prototype[S]=function(e){var r=this._abortAlgorithm(e);return _r(this),r},WritableStreamDefaultController.prototype[R]=function(){de(this)},WritableStreamDefaultController}();function hr(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof pr)}function _r(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function yr(e){return e._strategyHWM-e._queueTotalSize}function vr(e){var r=e._controlledWritableStream;if(e._started&&void 0===r._inFlightWriteRequest)if("erroring"!==r._state){if(0!==e._queue.length){var t=e._queue.peek().value;t===br?function(e){var r=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(r),se(e);var t=e._closeAlgorithm();_r(e),b(t,(function(){return function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";var r=e._writer;void 0!==r&&Or(r)}(r),null}),(function(e){return function(e,r){e._inFlightCloseRequest._reject(r),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(r),e._pendingAbortRequest=void 0),or(e,r)}(r,e),null}))}(e):function(e,r){var t=e._controlledWritableStream;(function(e){e._inFlightWriteRequest=e._writeRequests.shift()})(t),b(e._writeAlgorithm(r),(function(){!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(t);var r=t._state;if(se(e),!lr(t)&&"writable"===r){var n=gr(e);sr(t,n)}return vr(e),null}),(function(r){return"writable"===t._state&&_r(e),function(e,r){e._inFlightWriteRequest._reject(r),e._inFlightWriteRequest=void 0,or(e,r)}(t,r),null}))}(e,t)}}else ir(r)}function mr(e,r){"writable"===e._controlledWritableStream._state&&wr(e,r)}function gr(e){return yr(e)<=0}function wr(e,r){var t=e._controlledWritableStream;_r(e),ar(t,r)}function Sr(e){return new TypeError("WritableStream.prototype.".concat(e," can only be used on a WritableStream"))}function Rr(e){return new TypeError("WritableStreamDefaultController.prototype.".concat(e," can only be used on a WritableStreamDefaultController"))}function Tr(e){return new TypeError("WritableStreamDefaultWriter.prototype.".concat(e," can only be used on a WritableStreamDefaultWriter"))}function qr(e){return new TypeError("Cannot "+e+" a stream using a released writer")}function Pr(e){e._closedPromise=s((function(r,t){e._closedPromise_resolve=r,e._closedPromise_reject=t,e._closedPromiseState="pending"}))}function Cr(e,r){Pr(e),Er(e,r)}function Er(e,r){void 0!==e._closedPromise_reject&&(y(e._closedPromise),e._closedPromise_reject(r),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function Or(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function Wr(e){e._readyPromise=s((function(r,t){e._readyPromise_resolve=r,e._readyPromise_reject=t})),e._readyPromiseState="pending"}function kr(e,r){Wr(e),Br(e,r)}function jr(e){Wr(e),Ar(e)}function Br(e,r){void 0!==e._readyPromise_reject&&(y(e._readyPromise),e._readyPromise_reject(r),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function Ar(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(pr.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof e.toStringTag&&Object.defineProperty(pr.prototype,e.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});var zr="undefined"!=typeof DOMException?DOMException:void 0;var Lr,Fr=function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;try{return new e,!0}catch(e){return!1}}(zr)?zr:((Lr=function(e,r){this.message=e||"",this.name=r||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)}).prototype=Object.create(Error.prototype),Object.defineProperty(Lr.prototype,"constructor",{value:Lr,writable:!0,configurable:!0}),Lr);function Ir(e,r,t,n,o,a){var i=e.getReader(),l=r.getWriter();nt(e)&&(e._disturbed=!0);var u,h,m,g=!1,w=!1,S="readable",R="writable",T=!1,q=!1,P=s((function(e){m=e})),C=Promise.resolve(void 0);return s((function(E,O){var W;function k(){if(!g){var e=s((function(e,r){!function t(n){n?e():f(function(){if(g)return c(!0);return f(l.ready,(function(){return f(i.read(),(function(e){return!!e.done||(y(C=l.write(e.value)),!1)}))}))}(),t,r)}(!1)}));y(e)}}function j(){return S="closed",t?F():L((function(){return er(r)&&(T=lr(r),R=r._state),T||"closed"===R?c(void 0):"erroring"===R||"errored"===R?d(h):(T=!0,l.close())}),!1,void 0),null}function B(e){return g||(S="errored",u=e,n?F(!0,e):L((function(){return l.abort(e)}),!0,e)),null}function A(e){return w||(R="errored",h=e,o?F(!0,e):L((function(){return i.cancel(e)}),!0,e)),null}if(void 0!==a&&(W=function(){var e=void 0!==a.reason?a.reason:new Fr("Aborted","AbortError"),r=[];n||r.push((function(){return"writable"===R?l.abort(e):c(void 0)})),o||r.push((function(){return"readable"===S?i.cancel(e):c(void 0)})),L((function(){return Promise.all(r.map((function(e){return e()})))}),!0,e)},a.aborted?W():a.addEventListener("abort",W)),nt(e)&&(S=e._state,u=e._storedError),er(r)&&(R=r._state,h=r._storedError,T=lr(r)),nt(e)&&er(r)&&(q=!0,m()),"errored"===S)B(u);else if("erroring"===R||"errored"===R)A(h);else if("closed"===S)j();else if(T||"closed"===R){var z=new TypeError("the destination writable stream closed before all data could be piped to it");o?F(!0,z):L((function(){return i.cancel(z)}),!0,z)}function L(e,r,t){function n(){var e;return"writable"!==R||T?o():p(c(function r(){if(e!==C)return e=C,_(C,r,r)}()),o),null}function o(){return e?b(e(),(function(){return I(r,t)}),(function(e){return I(!0,e)})):I(r,t),null}g||(g=!0,q?n():p(P,n))}function F(e,r){L(void 0,e,r)}function I(e,r){return w=!0,l.releaseLock(),i.releaseLock(),void 0!==a&&a.removeEventListener("abort",W),e?O(r):E(void 0),null}g||(b(i.closed,j,B),b(l.closed,(function(){return w||(R="closed"),null}),A)),q?k():v((function(){q=!0,m(),k()}))}))}function Dr(e,r){return function(e){try{return e.getReader({mode:"byob"}).releaseLock(),!0}catch(e){return!1}}(e)?function(e){var r,t,n,o,a,i=e.getReader(),l=!1,u=!1,d=!1,f=!1,p=!1,_=!1,y=s((function(e){a=e}));function v(e){h(e.closed,(function(r){return e!==i||(n.error(r),o.error(r),p&&_||a(void 0)),null}))}function m(){l&&(i.releaseLock(),v(i=e.getReader()),l=!1),b(i.read(),(function(e){var r,t;if(d=!1,f=!1,e.done)return p||n.close(),_||o.close(),null===(r=n.byobRequest)||void 0===r||r.respond(0),null===(t=o.byobRequest)||void 0===t||t.respond(0),p&&_||a(void 0),null;var l=e.value,s=l,c=l;if(!p&&!_)try{c=ue(l)}catch(e){return n.error(e),o.error(e),a(i.cancel(e)),null}return p||n.enqueue(s),_||o.enqueue(c),u=!1,d?w():f&&S(),null}),(function(){return u=!1,null}))}function g(r,t){l||(i.releaseLock(),v(i=e.getReader({mode:"byob"})),l=!0);var s=t?o:n,c=t?n:o;b(i.read(r),(function(e){var r;d=!1,f=!1;var n=t?_:p,o=t?p:_;if(e.done){n||s.close(),o||c.close();var l=e.value;return void 0!==l&&(n||s.byobRequest.respondWithNewView(l),o||null===(r=c.byobRequest)||void 0===r||r.respond(0)),n&&o||a(void 0),null}var b=e.value;if(o)n||s.byobRequest.respondWithNewView(b);else{var h=void 0;try{h=ue(b)}catch(e){return s.error(e),c.error(e),a(i.cancel(e)),null}n||s.byobRequest.respondWithNewView(b),c.enqueue(h)}return u=!1,d?w():f&&S(),null}),(function(){return u=!1,null}))}function w(){if(u)return d=!0,c(void 0);u=!0;var e=n.byobRequest;return null===e?m():g(e.view,!1),c(void 0)}function S(){if(u)return f=!0,c(void 0);u=!0;var e=o.byobRequest;return null===e?m():g(e.view,!0),c(void 0)}function R(e){if(p=!0,r=e,_){var n=[r,t],o=i.cancel(n);a(o)}return y}function T(e){if(_=!0,t=e,p){var n=[r,t],o=i.cancel(n);a(o)}return y}var q=new tt({type:"bytes",start:function(e){n=e},pull:w,cancel:R}),P=new tt({type:"bytes",start:function(e){o=e},pull:S,cancel:T});return v(i),[q,P]}(e):function(e,r){var t,n,o,a,i,l=e.getReader(),u=!1,d=!1,f=!1,p=!1,_=s((function(e){i=e}));function y(){return u?(d=!0,c(void 0)):(u=!0,b(l.read(),(function(e){if(d=!1,e.done)return f||o.close(),p||a.close(),f&&p||i(void 0),null;var r=e.value,t=r,n=r;return f||o.enqueue(t),p||a.enqueue(n),u=!1,d&&y(),null}),(function(){return u=!1,null})),c(void 0))}function v(e){if(f=!0,t=e,p){var r=[t,n],o=l.cancel(r);i(o)}return _}function m(e){if(p=!0,n=e,f){var r=[t,n],o=l.cancel(r);i(o)}return _}var g=new tt({start:function(e){o=e},pull:y,cancel:v}),w=new tt({start:function(e){a=e},pull:y,cancel:m});return h(l.closed,(function(e){return o.error(e),a.error(e),f&&p||i(void 0),null})),[g,w]}(e)}var Mr=function(){function ReadableStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(ReadableStreamDefaultController.prototype,"desiredSize",{get:function(){if(!Qr(this))throw Gr("desiredSize");return xr(this)},enumerable:!1,configurable:!0}),ReadableStreamDefaultController.prototype.close=function(){if(!Qr(this))throw Gr("close");if(!Vr(this))throw new TypeError("The stream is not in a state that permits close");!function(e){if(!Vr(e))return;var r=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(Nr(e),it(r))}(this)},ReadableStreamDefaultController.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!Qr(this))throw Gr("enqueue");if(!Vr(this))throw new TypeError("The stream is not in a state that permits enqueue");return function(e,r){if(!Vr(e))return;var t=e._controlledReadableStream;if(ot(t)&&J(t)>0)X(t,r,!1);else{var n=void 0;try{n=e._strategySizeAlgorithm(r)}catch(r){throw Hr(e,r),r}try{ce(e,r,n)}catch(r){throw Hr(e,r),r}}Yr(e)}(this,e)},ReadableStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!Qr(this))throw Gr("error");Hr(this,e)},ReadableStreamDefaultController.prototype[T]=function(e){de(this);var r=this._cancelAlgorithm(e);return Nr(this),r},ReadableStreamDefaultController.prototype[q]=function(e){var r=this._controlledReadableStream;if(this._queue.length>0){var t=se(this);this._closeRequested&&0===this._queue.length?(Nr(this),it(r)):Yr(this),e._chunkSteps(t)}else G(r,e),Yr(this)},ReadableStreamDefaultController.prototype[P]=function(){},ReadableStreamDefaultController}();function Qr(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof Mr)}function Yr(e){var r=function(e){var r=e._controlledReadableStream;if(!Vr(e))return!1;if(!e._started)return!1;if(ot(r)&&J(r)>0)return!0;if(xr(e)>0)return!0;return!1}(e);r&&(e._pulling?e._pullAgain=!0:(e._pulling=!0,b(e._pullAlgorithm(),(function(){return e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,Yr(e)),null}),(function(r){return Hr(e,r),null}))))}function Nr(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function Hr(e,r){var t=e._controlledReadableStream;"readable"===t._state&&(de(e),Nr(e),lt(t,r))}function xr(e){var r=e._controlledReadableStream._state;return"errored"===r?null:"closed"===r?0:e._strategyHWM-e._queueTotalSize}function Vr(e){return!e._closeRequested&&"readable"===e._controlledReadableStream._state}function Ur(e,r,t,n){var o,a,i,l=Object.create(Mr.prototype);o=void 0!==r.start?function(){return r.start(l)}:function(){},a=void 0!==r.pull?function(){return r.pull(l)}:function(){return c(void 0)},i=void 0!==r.cancel?function(e){return r.cancel(e)}:function(){return c(void 0)},function(e,r,t,n,o,a,i){r._controlledReadableStream=e,r._queue=void 0,r._queueTotalSize=void 0,de(r),r._started=!1,r._closeRequested=!1,r._pullAgain=!1,r._pulling=!1,r._strategySizeAlgorithm=i,r._strategyHWM=a,r._pullAlgorithm=n,r._cancelAlgorithm=o,e._readableStreamController=r,b(c(t()),(function(){return r._started=!0,Yr(r),null}),(function(e){return Hr(r,e),null}))}(e,l,o,a,i,t,n)}function Gr(e){return new TypeError("ReadableStreamDefaultController.prototype.".concat(e," can only be used on a ReadableStreamDefaultController"))}function Xr(e,r,t){return I(e,t),function(t){return g(e,r,[t])}}function Jr(e,r,t){return I(e,t),function(t){return g(e,r,[t])}}function Kr(e,r,t){return I(e,t),function(t){return m(e,r,[t])}}function Zr(e,r){if("bytes"!==(e="".concat(e)))throw new TypeError("".concat(r," '").concat(e,"' is not a valid enumeration value for ReadableStreamType"));return e}function $r(e,r){if("byob"!==(e="".concat(e)))throw new TypeError("".concat(r," '").concat(e,"' is not a valid enumeration value for ReadableStreamReaderMode"));return e}function et(e,r){F(e,r);var t=null==e?void 0:e.preventAbort,n=null==e?void 0:e.preventCancel,o=null==e?void 0:e.preventClose,a=null==e?void 0:e.signal;return void 0!==a&&function(e,r){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError("".concat(r," is not an AbortSignal."))}(a,"".concat(r," has member 'signal' that")),{preventAbort:Boolean(t),preventCancel:Boolean(n),preventClose:Boolean(o),signal:a}}function rt(e,r){F(e,r);var t=null==e?void 0:e.readable;Q(t,"readable","ReadableWritablePair"),function(e,r){if(!x(e))throw new TypeError("".concat(r," is not a ReadableStream."))}(t,"".concat(r," has member 'readable' that"));var n=null==e?void 0:e.writable;return Q(n,"writable","ReadableWritablePair"),function(e,r){if(!V(e))throw new TypeError("".concat(r," is not a WritableStream."))}(n,"".concat(r," has member 'writable' that")),{readable:t,writable:n}}Object.defineProperties(Mr.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),o(Mr.prototype.close,"close"),o(Mr.prototype.enqueue,"enqueue"),o(Mr.prototype.error,"error"),"symbol"==typeof e.toStringTag&&Object.defineProperty(Mr.prototype,e.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});var tt=function(){function ReadableStream(e,r){void 0===e&&(e={}),void 0===r&&(r={}),void 0===e?e=null:D(e,"First parameter");var t,n=Ve(r,"Second parameter"),o=function(e,r){F(e,r);var t=e,n=null==t?void 0:t.autoAllocateChunkSize,o=null==t?void 0:t.cancel,a=null==t?void 0:t.pull,i=null==t?void 0:t.start,l=null==t?void 0:t.type;return{autoAllocateChunkSize:void 0===n?void 0:H(n,"".concat(r," has member 'autoAllocateChunkSize' that")),cancel:void 0===o?void 0:Xr(o,t,"".concat(r," has member 'cancel' that")),pull:void 0===a?void 0:Jr(a,t,"".concat(r," has member 'pull' that")),start:void 0===i?void 0:Kr(i,t,"".concat(r," has member 'start' that")),type:void 0===l?void 0:Zr(l,"".concat(r," has member 'type' that"))}}(e,"First parameter");if((t=this)._state="readable",t._reader=void 0,t._storedError=void 0,t._disturbed=!1,"bytes"===o.type){if(void 0!==n.size)throw new RangeError("The strategy for a byte stream cannot have a size function");Ae(this,o,He(n,0))}else{var a=xe(n);Ur(this,o,He(n,1),a)}}return Object.defineProperty(ReadableStream.prototype,"locked",{get:function(){if(!nt(this))throw ut("locked");return ot(this)},enumerable:!1,configurable:!0}),ReadableStream.prototype.cancel=function(e){return void 0===e&&(e=void 0),nt(this)?ot(this)?d(new TypeError("Cannot cancel a stream that already has a reader")):at(this,e):d(ut("cancel"))},ReadableStream.prototype.getReader=function(e){if(void 0===e&&(e=void 0),!nt(this))throw ut("getReader");return void 0===function(e,r){F(e,r);var t=null==e?void 0:e.mode;return{mode:void 0===t?void 0:$r(t,"".concat(r," has member 'mode' that"))}}(e,"First parameter").mode?new Z(this):function(e){return new Me(e)}(this)},ReadableStream.prototype.pipeThrough=function(e,r){if(void 0===r&&(r={}),!x(this))throw ut("pipeThrough");M(e,1,"pipeThrough");var t=rt(e,"First parameter"),n=et(r,"Second parameter");if(this.locked)throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(t.writable.locked)throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return y(Ir(this,t.writable,n.preventClose,n.preventAbort,n.preventCancel,n.signal)),t.readable},ReadableStream.prototype.pipeTo=function(e,r){if(void 0===r&&(r={}),!x(this))return d(ut("pipeTo"));if(void 0===e)return d("Parameter 1 is required in 'pipeTo'.");if(!V(e))return d(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));var t;try{t=et(r,"Second parameter")}catch(e){return d(e)}return this.locked?d(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):e.locked?d(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):Ir(this,e,t.preventClose,t.preventAbort,t.preventCancel,t.signal)},ReadableStream.prototype.tee=function(){if(!x(this))throw ut("tee");if(this.locked)throw new TypeError("Cannot tee a stream that already has a reader");return Dr(this)},ReadableStream.prototype.values=function(e){if(void 0===e&&(e=void 0),!x(this))throw ut("values");var r,t,n,o,a,i=function(e,r){F(e,r);var t=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(t)}}(e,"First parameter");return r=this,t=i.preventCancel,n=r.getReader(),o=new te(n,t),(a=Object.create(ne))._asyncIteratorImpl=o,a},ReadableStream}();function nt(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof tt)}function ot(e){return void 0!==e._reader}function at(e,t){if(e._disturbed=!0,"closed"===e._state)return c(void 0);if("errored"===e._state)return d(e._storedError);it(e);var n=e._reader;if(void 0!==n&&Qe(n)){var o=n._readIntoRequests;n._readIntoRequests=new w,o.forEach((function(e){e._closeSteps(void 0)}))}return _(e._readableStreamController[T](t),r)}function it(e){e._state="closed";var r=e._reader;if(void 0!==r&&(A(r),$(r))){var t=r._readRequests;r._readRequests=new w,t.forEach((function(e){e._closeSteps()}))}}function lt(e,r){e._state="errored",e._storedError=r;var t=e._reader;void 0!==t&&(B(t,r),$(t)?ee(t,r):Ye(t,r))}function ut(e){return new TypeError("ReadableStream.prototype.".concat(e," can only be used on a ReadableStream"))}function st(e,r){F(e,r);var t=null==e?void 0:e.highWaterMark;return Q(t,"highWaterMark","QueuingStrategyInit"),{highWaterMark:Y(t)}}Object.defineProperties(tt.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),o(tt.prototype.cancel,"cancel"),o(tt.prototype.getReader,"getReader"),o(tt.prototype.pipeThrough,"pipeThrough"),o(tt.prototype.pipeTo,"pipeTo"),o(tt.prototype.tee,"tee"),o(tt.prototype.values,"values"),"symbol"==typeof e.toStringTag&&Object.defineProperty(tt.prototype,e.toStringTag,{value:"ReadableStream",configurable:!0}),"symbol"==typeof e.asyncIterator&&Object.defineProperty(tt.prototype,e.asyncIterator,{value:tt.prototype.values,writable:!0,configurable:!0});var ct=function(e){return e.byteLength};o(ct,"size");var dt=function(){function ByteLengthQueuingStrategy(e){M(e,1,"ByteLengthQueuingStrategy"),e=st(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(ByteLengthQueuingStrategy.prototype,"highWaterMark",{get:function(){if(!bt(this))throw ft("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(ByteLengthQueuingStrategy.prototype,"size",{get:function(){if(!bt(this))throw ft("size");return ct},enumerable:!1,configurable:!0}),ByteLengthQueuingStrategy}();function ft(e){return new TypeError("ByteLengthQueuingStrategy.prototype.".concat(e," can only be used on a ByteLengthQueuingStrategy"))}function bt(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof dt)}Object.defineProperties(dt.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof e.toStringTag&&Object.defineProperty(dt.prototype,e.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});var pt=function(){return 1};o(pt,"size");var ht=function(){function CountQueuingStrategy(e){M(e,1,"CountQueuingStrategy"),e=st(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}return Object.defineProperty(CountQueuingStrategy.prototype,"highWaterMark",{get:function(){if(!yt(this))throw _t("highWaterMark");return this._countQueuingStrategyHighWaterMark},enumerable:!1,configurable:!0}),Object.defineProperty(CountQueuingStrategy.prototype,"size",{get:function(){if(!yt(this))throw _t("size");return pt},enumerable:!1,configurable:!0}),CountQueuingStrategy}();function _t(e){return new TypeError("CountQueuingStrategy.prototype.".concat(e," can only be used on a CountQueuingStrategy"))}function yt(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof ht)}function vt(e,r,t){return I(e,t),function(t){return g(e,r,[t])}}function mt(e,r,t){return I(e,t),function(t){return m(e,r,[t])}}function gt(e,r,t){return I(e,t),function(t,n){return g(e,r,[t,n])}}Object.defineProperties(ht.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof e.toStringTag&&Object.defineProperty(ht.prototype,e.toStringTag,{value:"CountQueuingStrategy",configurable:!0});var wt=function(){function TransformStream(e,r,t){void 0===e&&(e={}),void 0===r&&(r={}),void 0===t&&(t={}),void 0===e&&(e=null);var n=Ve(r,"Second parameter"),o=Ve(t,"Third parameter"),a=function(e,r){F(e,r);var t=null==e?void 0:e.flush,n=null==e?void 0:e.readableType,o=null==e?void 0:e.start,a=null==e?void 0:e.transform,i=null==e?void 0:e.writableType;return{flush:void 0===t?void 0:vt(t,e,"".concat(r," has member 'flush' that")),readableType:n,start:void 0===o?void 0:mt(o,e,"".concat(r," has member 'start' that")),transform:void 0===a?void 0:gt(a,e,"".concat(r," has member 'transform' that")),writableType:i}}(e,"First parameter");if(void 0!==a.readableType)throw new RangeError("Invalid readableType specified");if(void 0!==a.writableType)throw new RangeError("Invalid writableType specified");var i,l=He(o,0),u=xe(o),f=He(n,1),b=xe(n);!function(e,r,t,n,o,a){function i(){return r}function l(r){return function(e,r){var t=e._transformStreamController;if(e._backpressure){return _(e._backpressureChangePromise,(function(){if("erroring"===(er(e._writable)?e._writable._state:e._writableState))throw er(e._writable)?e._writable._storedError:e._writableStoredError;return Wt(t,r)}))}return Wt(t,r)}(e,r)}function u(r){return function(e,r){return Rt(e,r),c(void 0)}(e,r)}function s(){return function(e){var r=e._transformStreamController,t=r._flushAlgorithm();return Et(r),_(t,(function(){if("errored"===e._readableState)throw e._readableStoredError;Bt(e)&&At(e)}),(function(r){throw Rt(e,r),e._readableStoredError}))}(e)}function d(){return function(e){return qt(e,!1),e._backpressureChangePromise}(e)}function f(r){return Tt(e,r),c(void 0)}e._writableState="writable",e._writableStoredError=void 0,e._writableHasInFlightOperation=!1,e._writableStarted=!1,e._writable=function(e,r,t,n,o,a,i){return new $e({start:function(t){e._writableController=t;try{var n=t.signal;void 0!==n&&n.addEventListener("abort",(function(){"writable"===e._writableState&&(e._writableState="erroring",n.reason&&(e._writableStoredError=n.reason))}))}catch(e){}return _(r(),(function(){return e._writableStarted=!0,Mt(e),null}),(function(r){throw e._writableStarted=!0,Ft(e,r),r}))},write:function(r){return function(e){e._writableHasInFlightOperation=!0}(e),_(t(r),(function(){return function(e){e._writableHasInFlightOperation=!1}(e),Mt(e),null}),(function(r){throw function(e,r){e._writableHasInFlightOperation=!1,Ft(e,r)}(e,r),r}))},close:function(){return function(e){e._writableHasInFlightOperation=!0}(e),_(n(),(function(){return function(e){e._writableHasInFlightOperation=!1,"erroring"===e._writableState&&(e._writableStoredError=void 0);e._writableState="closed"}(e),null}),(function(r){throw function(e,r){e._writableHasInFlightOperation=!1,e._writableState,Ft(e,r)}(e,r),r}))},abort:function(r){return e._writableState="errored",e._writableStoredError=r,o(r)}},{highWaterMark:a,size:i})}(e,i,l,s,u,t,n),e._readableState="readable",e._readableStoredError=void 0,e._readableCloseRequested=!1,e._readablePulling=!1,e._readable=function(e,r,t,n,o,a){return new tt({start:function(t){return e._readableController=t,r().catch((function(r){zt(e,r)}))},pull:function(){return e._readablePulling=!0,t().catch((function(r){zt(e,r)}))},cancel:function(r){return e._readableState="closed",n(r)}},{highWaterMark:o,size:a})}(e,i,d,f,o,a),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,qt(e,!0),e._transformStreamController=void 0}(this,s((function(e){i=e})),f,b,l,u),function(e,r){var t,n,o=Object.create(Pt.prototype);t=void 0!==r.transform?function(e){return r.transform(e,o)}:function(e){try{return Ot(o,e),c(void 0)}catch(e){return d(e)}};n=void 0!==r.flush?function(){return r.flush(o)}:function(){return c(void 0)};!function(e,r,t,n){r._controlledTransformStream=e,e._transformStreamController=r,r._transformAlgorithm=t,r._flushAlgorithm=n}(e,o,t,n)}(this,a),void 0!==a.start?i(a.start(this._transformStreamController)):i(void 0)}return Object.defineProperty(TransformStream.prototype,"readable",{get:function(){if(!St(this))throw jt("readable");return this._readable},enumerable:!1,configurable:!0}),Object.defineProperty(TransformStream.prototype,"writable",{get:function(){if(!St(this))throw jt("writable");return this._writable},enumerable:!1,configurable:!0}),TransformStream}();function St(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof wt)}function Rt(e,r){zt(e,r),Tt(e,r)}function Tt(e,r){Et(e._transformStreamController),function(e,r){e._writableController.error(r),"writable"===e._writableState&&It(e,r)}(e,r),e._backpressure&&qt(e,!1)}function qt(e,r){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=s((function(r){e._backpressureChangePromise_resolve=r})),e._backpressure=r}Object.defineProperties(wt.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof e.toStringTag&&Object.defineProperty(wt.prototype,e.toStringTag,{value:"TransformStream",configurable:!0});var Pt=function(){function TransformStreamDefaultController(){throw new TypeError("Illegal constructor")}return Object.defineProperty(TransformStreamDefaultController.prototype,"desiredSize",{get:function(){if(!Ct(this))throw kt("desiredSize");return Lt(this._controlledTransformStream)},enumerable:!1,configurable:!0}),TransformStreamDefaultController.prototype.enqueue=function(e){if(void 0===e&&(e=void 0),!Ct(this))throw kt("enqueue");Ot(this,e)},TransformStreamDefaultController.prototype.error=function(e){if(void 0===e&&(e=void 0),!Ct(this))throw kt("error");var r;r=e,Rt(this._controlledTransformStream,r)},TransformStreamDefaultController.prototype.terminate=function(){if(!Ct(this))throw kt("terminate");!function(e){var r=e._controlledTransformStream;Bt(r)&&At(r);var t=new TypeError("TransformStream terminated");Tt(r,t)}(this)},TransformStreamDefaultController}();function Ct(e){return!!t(e)&&(!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof Pt)}function Et(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function Ot(e,r){var t=e._controlledTransformStream;if(!Bt(t))throw new TypeError("Readable side is not in a state that permits enqueue");try{!function(e,r){e._readablePulling=!1;try{e._readableController.enqueue(r)}catch(r){throw zt(e,r),r}}(t,r)}catch(e){throw Tt(t,e),t._readableStoredError}var n=function(e){return!function(e){if(!Bt(e))return!1;if(e._readablePulling)return!0;if(Lt(e)>0)return!0;return!1}(e)}(t);n!==t._backpressure&&qt(t,!0)}function Wt(e,r){return _(e._transformAlgorithm(r),void 0,(function(r){throw Rt(e._controlledTransformStream,r),r}))}function kt(e){return new TypeError("TransformStreamDefaultController.prototype.".concat(e," can only be used on a TransformStreamDefaultController"))}function jt(e){return new TypeError("TransformStream.prototype.".concat(e," can only be used on a TransformStream"))}function Bt(e){return!e._readableCloseRequested&&"readable"===e._readableState}function At(e){e._readableState="closed",e._readableCloseRequested=!0,e._readableController.close()}function zt(e,r){"readable"===e._readableState&&(e._readableState="errored",e._readableStoredError=r),e._readableController.error(r)}function Lt(e){return e._readableController.desiredSize}function Ft(e,r){"writable"!==e._writableState?Dt(e):It(e,r)}function It(e,r){e._writableState="erroring",e._writableStoredError=r,!function(e){return e._writableHasInFlightOperation}(e)&&e._writableStarted&&Dt(e)}function Dt(e){e._writableState="errored"}function Mt(e){"erroring"===e._writableState&&Dt(e)}Object.defineProperties(Pt.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),o(Pt.prototype.enqueue,"enqueue"),o(Pt.prototype.error,"error"),o(Pt.prototype.terminate,"terminate"),"symbol"==typeof e.toStringTag&&Object.defineProperty(Pt.prototype,e.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});export{dt as ByteLengthQueuingStrategy,ht as CountQueuingStrategy,be as ReadableByteStreamController,tt as ReadableStream,Me as ReadableStreamBYOBReader,fe as ReadableStreamBYOBRequest,Mr as ReadableStreamDefaultController,Z as ReadableStreamDefaultReader,wt as TransformStream,Pt as TransformStreamDefaultController,$e as WritableStream,pr as WritableStreamDefaultController,cr as WritableStreamDefaultWriter};
