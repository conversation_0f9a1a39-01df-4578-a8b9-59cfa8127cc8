#!/usr/bin/env python3
"""
Setup script to install and configure Ollama with Qwen 2.5 7B model.
"""

import subprocess
import sys
import time
import requests
import platform
from pathlib import Path


def run_command(command, check=True):
    """Run a shell command and return the result."""
    print(f"Running: {command}")
    try:
        result = subprocess.run(command, shell=True, check=check, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        return None


def check_ollama_installed():
    """Check if Ollama is installed."""
    result = run_command("ollama --version", check=False)
    return result is not None and result.returncode == 0


def install_ollama():
    """Install Ollama based on the operating system."""
    system = platform.system().lower()
    
    print(f"Installing Ollama for {system}...")
    
    if system == "linux" or system == "darwin":  # Linux or macOS
        # Download and run the install script
        install_cmd = "curl -fsSL https://ollama.ai/install.sh | sh"
        result = run_command(install_cmd)
        if result and result.returncode == 0:
            print("Ollama installed successfully!")
            return True
        else:
            print("Failed to install Ollama automatically.")
            print("Please visit https://ollama.ai for manual installation instructions.")
            return False
    
    elif system == "windows":
        print("For Windows, please download Ollama from https://ollama.ai")
        print("After installation, run this script again.")
        return False
    
    else:
        print(f"Unsupported operating system: {system}")
        print("Please visit https://ollama.ai for installation instructions.")
        return False


def start_ollama_service():
    """Start the Ollama service."""
    print("Starting Ollama service...")
    
    # Try to start Ollama in the background
    system = platform.system().lower()
    
    if system == "linux":
        # On Linux, Ollama might be installed as a systemd service
        result = run_command("systemctl --user start ollama", check=False)
        if result and result.returncode != 0:
            # If systemd service doesn't exist, try running directly
            print("Starting Ollama directly...")
            subprocess.Popen(["ollama", "serve"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    else:
        # On macOS and Windows, start Ollama directly
        print("Starting Ollama server...")
        subprocess.Popen(["ollama", "serve"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
    
    # Wait a moment for the service to start
    time.sleep(3)


def check_ollama_running():
    """Check if Ollama service is running."""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        return response.status_code == 200
    except:
        return False


def pull_qwen_model():
    """Pull the Qwen 2.5 7B model."""
    print("Pulling Qwen 2.5 7B model... This may take a while (several GB download)")
    
    result = run_command("ollama pull qwen2.5:7b")
    if result and result.returncode == 0:
        print("Qwen 2.5 7B model downloaded successfully!")
        return True
    else:
        print("Failed to download Qwen 2.5 7B model.")
        return False


def test_model():
    """Test the model with a simple query."""
    print("Testing the model...")
    
    test_prompt = "Hello, can you help me with function calling?"
    result = run_command(f'ollama run qwen2.5:7b "{test_prompt}"')
    
    if result and result.returncode == 0:
        print("Model test successful!")
        return True
    else:
        print("Model test failed.")
        return False


def main():
    """Main setup function."""
    print("=== Ollama Setup for AI Function Calling Pipeline ===\n")
    
    # Check if Ollama is already installed
    if not check_ollama_installed():
        print("Ollama not found. Installing...")
        if not install_ollama():
            print("Installation failed. Please install Ollama manually.")
            sys.exit(1)
    else:
        print("Ollama is already installed.")
    
    # Start Ollama service
    if not check_ollama_running():
        start_ollama_service()
        
        # Wait and check again
        print("Waiting for Ollama service to start...")
        time.sleep(5)
        
        if not check_ollama_running():
            print("Failed to start Ollama service.")
            print("Please start Ollama manually with: ollama serve")
            sys.exit(1)
    
    print("Ollama service is running.")
    
    # Pull the Qwen model
    if not pull_qwen_model():
        print("Failed to download the model. Please try manually: ollama pull qwen2.5:7b")
        sys.exit(1)
    
    # Test the model
    if not test_model():
        print("Model test failed, but installation might still be successful.")
    
    print("\n=== Setup Complete! ===")
    print("Ollama is installed and Qwen 2.5 7B model is ready.")
    print("You can now run the AI Function Calling Pipeline:")
    print("  python -m src.main")
    print("\nOr install dependencies first:")
    print("  pip install -r requirements.txt")
    print("  python -m src.main")


if __name__ == "__main__":
    main()
