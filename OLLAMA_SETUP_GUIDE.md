# 🤖 Ollama Setup Guide for AI Function Calling Pipeline

Since you already have Ollama downloaded, here's how to complete the setup and enable full AI functionality.

## 📋 **Quick Setup Steps**

### 1. **Install/Start Ollama**
If Ollama is downloaded but not installed:
- **Windows**: Run the Ollama installer (.exe file)
- **macOS**: Open the .dmg file and drag to Applications
- **Linux**: Follow the installation instructions from ollama.ai

### 2. **Start Ollama Service**
Open a terminal/command prompt and run:
```bash
ollama serve
```
This starts the Ollama service on `http://localhost:11434`

### 3. **Download Qwen 2.5 7B Model**
In another terminal, run:
```bash
ollama pull qwen2.5:7b
```
⚠️ **Note**: This will download ~4.7GB, so ensure you have good internet and sufficient disk space.

### 4. **Verify Installation**
Test that everything is working:
```bash
ollama list
ollama run qwen2.5:7b "Hello, can you help with function calling?"
```

### 5. **Start the AI Pipeline**
```bash
cd "d:\D Drive\Projects\codemate"
python start_main_server.py
```

### 6. **Access the Full AI Interface**
Open your browser to: http://localhost:8000

---

## 🔧 **Troubleshooting**

### **If Ollama command not found:**
1. **Windows**: Add Ollama to PATH or use full path:
   ```cmd
   "C:\Users\<USER>\AppData\Local\Programs\Ollama\ollama.exe" serve
   ```

2. **Check installation location**:
   - Windows: `C:\Users\<USER>\AppData\Local\Programs\Ollama\`
   - macOS: `/Applications/Ollama.app/`
   - Linux: `/usr/local/bin/ollama`

### **If model download fails:**
- Check internet connection
- Ensure sufficient disk space (5GB+)
- Try: `ollama pull qwen2.5:7b --verbose`

### **If service won't start:**
- Check if port 11434 is available
- Try running as administrator (Windows)
- Check firewall settings

---

## 🚀 **What Happens When AI is Enabled**

Once Ollama + Qwen 2.5 7B is running, the pipeline will:

### **✨ Natural Language Processing**
- Convert queries like "Get March invoices and email summary" into structured function calls
- Understand complex multi-step workflows
- Generate reasoning for each execution plan

### **🧠 Intelligent Function Selection**
- Automatically choose the right functions from 60+ available
- Handle parameter mapping and dependencies
- Optimize execution sequences

### **📊 Real-time Query Processing**
- Process queries through the web interface
- Generate execution plans in seconds
- Show AI reasoning and function sequences

---

## 🎯 **Example Queries to Try**

Once AI is enabled, try these natural language queries:

### **Business Operations**
- "Retrieve all invoices for March, calculate total, and send summary email"
- "Generate a financial report for Q1 and export to PDF"
- "Process pending payments and update customer records"

### **System Administration**
- "Check system status, backup database if needed, and log results"
- "Monitor disk space and clean up temp files if usage is high"
- "Create system backup and send notification when complete"

### **Data Analysis**
- "Get sales data for last quarter, calculate trends, and create charts"
- "Analyze customer data, generate insights, and create dashboard"
- "Export analytics data and schedule automated reports"

### **Communication & Scheduling**
- "Send reminder emails to all pending invoice customers"
- "Schedule team meeting for next week and send calendar invites"
- "Create task reminders and notify team members"

---

## 📈 **Performance Tips**

### **For Best Results:**
- Use specific, clear language in queries
- Include relevant details (dates, names, etc.)
- Break very complex requests into smaller parts

### **System Requirements:**
- **RAM**: 8GB+ recommended for Qwen 2.5 7B
- **Storage**: 5GB+ free space for model
- **CPU**: Modern multi-core processor recommended

---

## 🔄 **Current Status vs. AI-Enabled**

| Feature | Current Status | With AI Enabled |
|---------|---------------|-----------------|
| **Function Library** | ✅ 60 functions working | ✅ Same + AI selection |
| **Execution Engine** | ✅ Manual function calls | ✅ AI-generated sequences |
| **Web Interface** | ✅ Function browser | ✅ Natural language input |
| **API Endpoints** | ✅ Structured requests | ✅ Text-to-function conversion |
| **Query Processing** | ❌ Manual planning | ✅ AI-powered planning |

---

## 🎉 **Ready to Enable AI?**

Your AI Function Calling Pipeline is **100% ready** for AI integration!

Just follow the steps above to:
1. Start Ollama service
2. Download Qwen 2.5 7B model  
3. Restart the pipeline
4. Enjoy natural language → function calling magic! ✨

The pipeline will automatically detect when Ollama is available and enable all AI features.
