# AI Function Calling Pipeline

A sophisticated pipeline that leverages the Qwen 2.5 7B model to process natural language queries and return structured sequences of function calls.

## Overview

This project creates an intelligent system that:
- Interprets natural language user queries
- Breaks down complex requests into discrete tasks
- Maps tasks to appropriate functions from a comprehensive library
- Returns logically structured execution sequences
- Provides a web interface for easy interaction

## Architecture

```
User Query → Query Parser → AI Planning Engine → Function Sequencer → Execution Engine → Response
```

## Features

- **50+ Function Library**: Comprehensive set of functions covering data operations, communication, file handling, web operations, analytics, and more
- **Intelligent Planning**: Uses Qwen 2.5 7B model for sophisticated query understanding and task decomposition
- **Structured Output**: Returns well-formatted function call sequences with input/output mapping
- **Web Interface**: Simple and intuitive interface for testing and demonstration
- **Local Deployment**: Runs entirely locally using Ollama

## Technology Stack

- **AI Model**: Qwen 2.5 7B (via Ollama)
- **Backend**: Python, FastAPI
- **Frontend**: HTML/CSS/JavaScript
- **Function Calling**: Custom implementation with schema validation
- **Dependencies**: Pydantic, Requests, etc.

## Quick Start

1. Install Ollama and pull Qwen 2.5 7B model
2. Install Python dependencies
3. Run the pipeline server
4. Access the web interface

## Example Usage

**Query**: "Retrieve all invoices for March, summarize the total amount, and send the summary to my email."

**Generated Sequence**:
1. `retrieve_invoices(month="March", year=2024)`
2. `calculate_sum(data=invoices, field="amount")`
3. `send_email(to="<EMAIL>", subject="March Invoice Summary", body=summary)`

## Project Structure

```
codemate/
├── src/
│   ├── pipeline/          # Core pipeline logic
│   ├── functions/         # Function library
│   ├── models/           # Data models and schemas
│   └── api/              # FastAPI endpoints
├── web/                  # Frontend interface
├── tests/                # Unit tests
├── docs/                 # Documentation
└── requirements.txt      # Dependencies
```

## Installation

### Prerequisites
- Python 3.8+
- Git

### Quick Setup (Automated)

1. **Clone the Repository**:
```bash
git clone <repository-url>
cd codemate
```

2. **Run Automated Setup**:
```bash
# This will install Ollama, download Qwen 2.5 7B, and set everything up
python setup_ollama.py
```

3. **Install Python Dependencies**:
```bash
pip install -r requirements.txt
```

4. **Start the Pipeline**:
```bash
python -m src.main
```

5. **Access Web Interface**:
Open http://localhost:8000 in your browser

### Manual Setup

If the automated setup doesn't work for your system:

1. **Install Ollama**:
   - Visit https://ollama.ai
   - Download and install for your platform
   - Start Ollama: `ollama serve`

2. **Download Model**:
```bash
ollama pull qwen2.5:7b
```

3. **Install Dependencies and Run**:
```bash
pip install -r requirements.txt
python -m src.main
```

## Function Library

The pipeline includes 50+ functions organized into categories:

- **Data Operations**: retrieve, filter, sort, aggregate, transform
- **Communication**: email, SMS, notifications, messaging
- **File Operations**: read, write, convert, compress, backup
- **Web Operations**: scrape, API calls, search, download
- **Analytics**: calculate, summarize, visualize, report
- **Database**: CRUD operations, queries, migrations
- **Scheduling**: calendar, reminders, tasks, events
- **Financial**: invoices, payments, reports, calculations
- **System**: monitoring, logging, backup, maintenance

## API Endpoints

- `POST /process-query`: Process natural language query and return function sequence
- `GET /functions`: List all available functions with schemas
- `POST /execute`: Execute a function sequence (optional)
- `GET /health`: Health check endpoint

## Usage Examples

### Via Web Interface
1. Open http://localhost:8000
2. Enter a natural language query
3. Click "Generate Plan" to see the execution sequence
4. Click "Execute Plan" to run the functions
5. Use "Dry Run" to simulate execution without side effects

### Via API

**Process Query**:
```bash
curl -X POST "http://localhost:8000/api/process-query" \
  -H "Content-Type: application/json" \
  -d '{"query": "Retrieve all invoices for March and calculate total amount"}'
```

**Get Function Library**:
```bash
curl "http://localhost:8000/api/functions"
```

**Health Check**:
```bash
curl "http://localhost:8000/api/health"
```

### Example Queries

1. **Data Processing**: "Retrieve sales data for Q1, filter by region 'North', and calculate average revenue"

2. **Communication**: "Send an <NAME_EMAIL> with subject 'Weekly Report' and attach the latest analytics"

3. **System Operations**: "Check disk space, create a backup if usage is above 80%, and log the results"

4. **Financial**: "Get all unpaid invoices, calculate late fees, and send reminder emails to customers"

5. **Scheduling**: "Create a meeting for next Tuesday at 2 PM with the development team and set a reminder"

## Testing

Run the test suite:
```bash
# Install test dependencies
pip install pytest

# Run tests
python -m pytest tests/ -v

# Run specific test
python -m pytest tests/test_pipeline.py::TestFunctions::test_data_operations -v
```

## Architecture Details

### Core Components

1. **AI Client** (`src/pipeline/ai_client.py`):
   - Communicates with Ollama/Qwen 2.5 7B
   - Handles prompt engineering for function calling
   - Parses AI responses into structured plans

2. **Function Registry** (`src/functions/registry.py`):
   - Manages 50+ available functions
   - Validates function calls and parameters
   - Provides function discovery and search

3. **Execution Engine** (`src/pipeline/executor.py`):
   - Executes function call sequences
   - Manages variable dependencies
   - Provides dry-run capabilities

4. **Core Pipeline** (`src/pipeline/core.py`):
   - Orchestrates the entire process
   - Handles query processing and execution
   - Provides health monitoring

### Function Categories

- **data_operations** (8 functions): retrieve_data, filter_data, sort_data, group_data, aggregate_data, transform_data, merge_data, deduplicate_data
- **communication** (8 functions): send_email, send_sms, send_notification, create_message_template, send_templated_message, send_bulk_messages, schedule_message, get_message_status
- **file_operations** (9 functions): read_file, write_file, copy_file, move_file, delete_file, list_files, get_file_info, compress_files, extract_archive
- **web_operations** (4 functions): make_http_request, scrape_webpage, download_file, search_web
- **analytics** (6 functions): calculate_statistics, create_chart, generate_report, analyze_trends, create_dashboard, export_data
- **database** (6 functions): execute_query, insert_record, update_record, delete_record, backup_database, restore_database
- **scheduling** (5 functions): create_calendar_event, schedule_task, create_reminder, get_schedule, cancel_event
- **financial** (7 functions): retrieve_invoices, calculate_total_amount, generate_invoice, process_payment, generate_financial_report, calculate_tax, track_expenses
- **system_operations** (7 functions): get_system_status, create_log_entry, monitor_service, create_backup, restart_service, cleanup_temp_files, check_disk_space

## Troubleshooting

### Common Issues

1. **"AI service not available"**:
   - Ensure Ollama is running: `ollama serve`
   - Check if model is downloaded: `ollama list`
   - Verify connection: `curl http://localhost:11434/api/tags`

2. **"Function not found" errors**:
   - Check function name spelling
   - Verify function is registered in the appropriate module
   - Restart the pipeline to reload functions

3. **Slow response times**:
   - Qwen 2.5 7B requires significant compute resources
   - Consider using a smaller model for testing
   - Ensure sufficient RAM (8GB+ recommended)

4. **JSON parsing errors**:
   - The AI model occasionally returns malformed JSON
   - The pipeline includes fallback parsing mechanisms
   - Try rephrasing your query if issues persist

### Performance Tips

- Use specific, clear queries for better AI understanding
- Break complex requests into simpler components
- Use dry-run mode to test plans before execution
- Monitor system resources when running the model

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Add your changes and tests
4. Ensure all tests pass (`python -m pytest`)
5. Submit a pull request

### Adding New Functions

1. Create function implementation in appropriate module (e.g., `src/functions/your_category.py`)
2. Register function in the module's `register_functions()` method
3. Add tests in `tests/test_pipeline.py`
4. Update documentation

## License

MIT License - see LICENSE file for details

## Acknowledgments

- **Qwen Team** for the excellent Qwen 2.5 model
- **Ollama** for making local AI deployment simple
- **FastAPI** for the robust web framework
- **Anthropic** for inspiration from Claude's function calling capabilities
