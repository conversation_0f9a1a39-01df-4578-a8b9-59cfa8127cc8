#!/usr/bin/env python3
"""
Start the AI Function Calling Pipeline server with Groq integration.
"""

import sys
import os
from pathlib import Path

# Setup paths
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))
os.chdir(project_root)

def check_groq_setup():
    """Check if Groq is properly set up."""
    api_key = os.getenv("GROQ_API_KEY")
    if not api_key:
        print("❌ GROQ_API_KEY environment variable not found!")
        print("\nTo set up Groq:")
        print("1. Get API key from: https://console.groq.com/")
        print("2. Set environment variable:")
        print("   Windows: set GROQ_API_KEY=your_api_key")
        print("   Linux/Mac: export GROQ_API_KEY=your_api_key")
        print("3. Restart this script")
        return False
    
    print(f"✅ GROQ_API_KEY found: {api_key[:8]}...")
    return True

def main():
    print("🚀 AI FUNCTION CALLING PIPELINE - GROQ EDITION")
    print("=" * 50)
    print("Starting server with Groq's lightning-fast inference...")
    
    # Check Groq setup
    if not check_groq_setup():
        return
    
    try:
        # Test Groq connection
        print("\n🤖 Testing Groq connection...")
        from src.pipeline.groq_client import GroqClient
        
        client = GroqClient()
        if client.is_available():
            print(f"✅ Groq API connected with model: {client.model}")
        else:
            print("❌ Groq API not available. Check your API key.")
            return
        
        # Test pipeline
        print("🔧 Initializing pipeline...")
        from src.pipeline.core import FunctionCallingPipeline
        
        pipeline = FunctionCallingPipeline(use_groq=True)
        health = pipeline.get_health_status()
        
        print(f"✅ Pipeline status: {health['status']}")
        print(f"✅ AI Provider: {health['ai_provider']}")
        print(f"✅ Model: {health['model']}")
        print(f"✅ Functions loaded: {health['functions_loaded']}")
        
        # Start server
        print("\n🌐 Starting web server...")
        print("Server URL: http://localhost:8000")
        print("API Docs: http://localhost:8000/docs")
        print("Press Ctrl+C to stop")
        print()
        
        import uvicorn
        from src.api.routes import app
        
        # Override the default pipeline in routes to use Groq
        import src.api.routes as routes_module
        routes_module.pipeline = pipeline
        
        uvicorn.run(
            app,
            host="127.0.0.1",
            port=8000,
            log_level="info",
            access_log=True
        )
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
