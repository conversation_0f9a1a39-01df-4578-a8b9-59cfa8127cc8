"""
Groq AI client for fast language model inference.
"""

import json
import os
from typing import Dict, Any, List, Optional
import logging
from groq import Groq

logger = logging.getLogger(__name__)


class GroqClient:
    """Client for interacting with Groq API for fast inference."""
    
    def __init__(self, api_key: str = None, model: str = "llama-3.3-70b-versatile"):
        self.api_key = api_key or os.getenv("GROQ_API_KEY")
        self.model = model
        
        if not self.api_key:
            logger.warning("GROQ_API_KEY not found. Set environment variable or pass api_key parameter.")
            self.client = None
        else:
            self.client = Groq(api_key=self.api_key)
    
    def is_available(self) -> bool:
        """Check if Groq API is available."""
        if not self.client:
            return False
        
        try:
            # Test with a simple completion
            response = self.client.chat.completions.create(
                messages=[{"role": "user", "content": "Hello"}],
                model=self.model,
                max_tokens=10
            )
            return True
        except Exception as e:
            logger.error(f"Groq API not available: {e}")
            return False
    
    def chat_completion(self, messages: List[Dict[str, str]], temperature: float = 0.1, max_tokens: int = 1000) -> str:
        """Generate chat completion using Groq API."""
        if not self.client:
            logger.error("Groq client not initialized. Check API key.")
            return ""
        
        try:
            response = self.client.chat.completions.create(
                messages=messages,
                model=self.model,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=0.9
            )
            
            return response.choices[0].message.content or ""
            
        except Exception as e:
            logger.error(f"Error generating completion: {e}")
            return ""


class GroqAIPlanner:
    """AI-powered query planner using Groq's fast inference."""
    
    def __init__(self, groq_client: GroqClient):
        self.client = groq_client
        self.system_prompt = self._build_system_prompt()
    
    def _build_system_prompt(self) -> str:
        """Build the system prompt for function calling."""
        return """You are a function calling assistant. Convert user queries into JSON function call sequences.

TASK: Analyze the query and create a structured execution plan.

AVAILABLE FUNCTIONS:
- data_operations: retrieve_data, filter_data, aggregate_data, sort_data, group_data, transform_data, merge_data, deduplicate_data
- communication: send_email, send_sms, send_notification, create_message_template, send_templated_message, send_bulk_messages, schedule_message, get_message_status
- financial: retrieve_invoices, calculate_total_amount, generate_invoice, process_payment, generate_financial_report, calculate_tax, track_expenses
- file_operations: read_file, write_file, copy_file, move_file, delete_file, list_files, get_file_info, compress_files, extract_archive
- analytics: calculate_statistics, create_chart, generate_report, analyze_trends, create_dashboard, export_data
- database: execute_query, insert_record, update_record, delete_record, backup_database, restore_database
- scheduling: create_calendar_event, schedule_task, create_reminder, get_schedule, cancel_event
- system_operations: get_system_status, create_log_entry, monitor_service, create_backup, restart_service, cleanup_temp_files, check_disk_space
- web_operations: make_http_request, scrape_webpage, download_file, search_web

RESPONSE FORMAT (JSON only):
{
    "reasoning": "Brief explanation of the plan",
    "function_calls": [
        {
            "function_name": "function_name",
            "parameters": {"param": "value"},
            "output_variable": "var_name",
            "depends_on": []
        }
    ]
}

RULES:
- Return ONLY valid JSON
- Use simple, clear variable names
- Include all required parameters
- Order functions logically
- Keep reasoning brief and clear"""
    
    def plan_execution(self, query: str, available_functions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Plan execution sequence for a user query using Groq."""
        
        # Build function reference for the prompt (simplified for speed)
        function_names = [func['name'] for func in available_functions]
        functions_summary = ", ".join(function_names[:20])  # Limit for faster processing
        if len(function_names) > 20:
            functions_summary += f" and {len(function_names) - 20} more..."
        
        prompt = f"""User Query: "{query}"

Available Functions: {functions_summary}

Convert this query to a JSON execution plan. Return ONLY the JSON response."""
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": prompt}
        ]
        
        response = self.client.chat_completion(messages, temperature=0.1, max_tokens=800)
        
        try:
            # Clean up response
            response = response.strip()
            
            # Remove markdown code blocks if present
            if response.startswith("```json"):
                response = response[7:]
            if response.startswith("```"):
                response = response[3:]
            if response.endswith("```"):
                response = response[:-3]
            
            response = response.strip()
            
            # Parse JSON
            plan = json.loads(response)
            
            # Validate the plan structure
            if "function_calls" not in plan:
                raise ValueError("Missing function_calls in response")
            
            return {
                "success": True,
                "plan": plan,
                "raw_response": response
            }
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Raw response: {response}")
            
            # Try to extract JSON from response
            try:
                start = response.find("{")
                end = response.rfind("}") + 1
                if start >= 0 and end > start:
                    json_part = response[start:end]
                    plan = json.loads(json_part)
                    return {
                        "success": True,
                        "plan": plan,
                        "raw_response": response
                    }
            except:
                pass
            
            return {
                "success": False,
                "error": f"Failed to parse AI response: {str(e)}",
                "raw_response": response
            }
        
        except Exception as e:
            logger.error(f"Error in plan execution: {e}")
            return {
                "success": False,
                "error": str(e),
                "raw_response": response
            }
