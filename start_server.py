#!/usr/bin/env python3
"""
Simple server startup script for the AI Function Calling Pipeline.
"""

import sys
import os
from pathlib import Path

# Setup paths
project_root = Path(__file__).parent.absolute()
sys.path.insert(0, str(project_root))
os.chdir(project_root)

print("Starting AI Function Calling Pipeline...")
print(f"Working directory: {os.getcwd()}")

try:
    # Test basic functionality
    from src.functions.registry import function_registry
    from src.pipeline.core import FunctionCallingPipeline
    
    functions = function_registry.list_functions()
    print(f"Function registry: {len(functions)} functions loaded")
    
    pipeline = FunctionCallingPipeline()
    library = pipeline.get_function_library()
    print(f"Pipeline initialized with {library['total_functions']} functions")
    
    # Start server
    import uvicorn
    from src.api.routes import app
    
    print("\nStarting web server...")
    print("Server URL: http://localhost:8000")
    print("API Docs: http://localhost:8000/docs")
    print("Press Ctrl+C to stop\n")
    
    uvicorn.run(
        app, 
        host="127.0.0.1", 
        port=8000, 
        log_level="info",
        access_log=True
    )
    
except KeyboardInterrupt:
    print("\nServer stopped by user")
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
