"""Web operations functions for the AI pipeline."""

from typing import Any, Dict, List, Optional
from src.models.schemas import FunctionSchema, FunctionParameter, FunctionParameterType
from datetime import datetime


def make_http_request(url: str, method: str = "GET", headers: Dict[str, str] = None, data: Any = None) -> Dict[str, Any]:
    """Make an HTTP request to a URL."""
    return {"success": True, "status_code": 200, "response": "Mock response", "url": url, "method": method}


def scrape_webpage(url: str, selectors: List[str] = None) -> Dict[str, Any]:
    """Scrape content from a webpage."""
    return {"success": True, "url": url, "content": "Mock scraped content", "scraped_at": datetime.now().isoformat()}


def download_file(url: str, destination: str) -> Dict[str, Any]:
    """Download a file from a URL."""
    return {"success": True, "url": url, "destination": destination, "size": 1024, "downloaded_at": datetime.now().isoformat()}


def search_web(query: str, num_results: int = 10) -> List[Dict[str, Any]]:
    """Search the web for information."""
    return [{"title": f"Result {i}", "url": f"https://example.com/{i}", "snippet": f"Mock result {i}"} for i in range(num_results)]


def register_functions(registry):
    """Register web operation functions."""
    registry.register_function(
        FunctionSchema(name="make_http_request", description="Make an HTTP request", category="web_operations",
                      parameters=[FunctionParameter(name="url", type=FunctionParameterType.STRING, description="Target URL"),
                                FunctionParameter(name="method", type=FunctionParameterType.STRING, description="HTTP method", required=False, default="GET")],
                      returns="HTTP response", examples=["make_http_request('https://api.example.com/data')"]),
        make_http_request
    )
    registry.register_function(
        FunctionSchema(name="scrape_webpage", description="Scrape content from webpage", category="web_operations",
                      parameters=[FunctionParameter(name="url", type=FunctionParameterType.STRING, description="Webpage URL")],
                      returns="Scraped content", examples=["scrape_webpage('https://example.com')"]),
        scrape_webpage
    )
    registry.register_function(
        FunctionSchema(name="download_file", description="Download file from URL", category="web_operations",
                      parameters=[FunctionParameter(name="url", type=FunctionParameterType.STRING, description="File URL"),
                                FunctionParameter(name="destination", type=FunctionParameterType.STRING, description="Save location")],
                      returns="Download result", examples=["download_file('https://example.com/file.pdf', '/downloads/')"]),
        download_file
    )
    registry.register_function(
        FunctionSchema(name="search_web", description="Search the web", category="web_operations",
                      parameters=[FunctionParameter(name="query", type=FunctionParameterType.STRING, description="Search query"),
                                FunctionParameter(name="num_results", type=FunctionParameterType.INTEGER, description="Number of results", required=False, default=10)],
                      returns="Search results", examples=["search_web('AI function calling', 5)"]),
        search_web
    )
