"""
Data operations functions for the AI pipeline.
"""

from typing import Any, Dict, List, Union
from src.models.schemas import FunctionSchema, FunctionParameter, FunctionParameterType
import json
import csv
from datetime import datetime, timedelta


def retrieve_data(source: str, filters: Dict[str, Any] = None, limit: int = None) -> List[Dict[str, Any]]:
    """Retrieve data from a specified source with optional filtering."""
    # Mock implementation - in real scenario, this would connect to actual data sources
    mock_data = [
        {"id": 1, "name": "Item 1", "category": "A", "value": 100, "date": "2024-03-01"},
        {"id": 2, "name": "Item 2", "category": "B", "value": 200, "date": "2024-03-15"},
        {"id": 3, "name": "Item 3", "category": "A", "value": 150, "date": "2024-03-20"},
    ]
    
    result = mock_data.copy()
    
    # Apply filters
    if filters:
        for key, value in filters.items():
            result = [item for item in result if item.get(key) == value]
    
    # Apply limit
    if limit:
        result = result[:limit]
    
    return result


def filter_data(data: List[Dict[str, Any]], conditions: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Filter data based on specified conditions."""
    result = []
    for item in data:
        match = True
        for key, value in conditions.items():
            if key not in item or item[key] != value:
                match = False
                break
        if match:
            result.append(item)
    return result


def sort_data(data: List[Dict[str, Any]], sort_by: str, ascending: bool = True) -> List[Dict[str, Any]]:
    """Sort data by a specified field."""
    return sorted(data, key=lambda x: x.get(sort_by, 0), reverse=not ascending)


def group_data(data: List[Dict[str, Any]], group_by: str) -> Dict[str, List[Dict[str, Any]]]:
    """Group data by a specified field."""
    groups = {}
    for item in data:
        key = item.get(group_by, "unknown")
        if key not in groups:
            groups[key] = []
        groups[key].append(item)
    return groups


def aggregate_data(data: List[Dict[str, Any]], field: str, operation: str) -> Union[float, int]:
    """Aggregate data using specified operation (sum, avg, count, min, max)."""
    values = [item.get(field, 0) for item in data if field in item]
    
    if operation == "sum":
        return sum(values)
    elif operation == "avg":
        return sum(values) / len(values) if values else 0
    elif operation == "count":
        return len(values)
    elif operation == "min":
        return min(values) if values else 0
    elif operation == "max":
        return max(values) if values else 0
    else:
        raise ValueError(f"Unsupported operation: {operation}")


def transform_data(data: List[Dict[str, Any]], transformations: Dict[str, str]) -> List[Dict[str, Any]]:
    """Transform data fields based on specified transformations."""
    result = []
    for item in data.copy():
        new_item = item.copy()
        for field, transformation in transformations.items():
            if field in new_item:
                if transformation == "uppercase":
                    new_item[field] = str(new_item[field]).upper()
                elif transformation == "lowercase":
                    new_item[field] = str(new_item[field]).lower()
                elif transformation == "round":
                    new_item[field] = round(float(new_item[field]), 2)
        result.append(new_item)
    return result


def merge_data(data1: List[Dict[str, Any]], data2: List[Dict[str, Any]], join_key: str) -> List[Dict[str, Any]]:
    """Merge two datasets on a common key."""
    result = []
    data2_dict = {item[join_key]: item for item in data2 if join_key in item}
    
    for item1 in data1:
        if join_key in item1 and item1[join_key] in data2_dict:
            merged_item = {**item1, **data2_dict[item1[join_key]]}
            result.append(merged_item)
    
    return result


def deduplicate_data(data: List[Dict[str, Any]], key: str = None) -> List[Dict[str, Any]]:
    """Remove duplicate entries from data."""
    if key:
        seen = set()
        result = []
        for item in data:
            if key in item and item[key] not in seen:
                seen.add(item[key])
                result.append(item)
        return result
    else:
        # Remove exact duplicates
        seen = set()
        result = []
        for item in data:
            item_str = json.dumps(item, sort_keys=True)
            if item_str not in seen:
                seen.add(item_str)
                result.append(item)
        return result


def register_functions(registry):
    """Register all data operation functions."""
    
    # Retrieve data
    registry.register_function(
        FunctionSchema(
            name="retrieve_data",
            description="Retrieve data from a specified source with optional filtering",
            category="data_operations",
            parameters=[
                FunctionParameter(name="source", type=FunctionParameterType.STRING, description="Data source identifier"),
                FunctionParameter(name="filters", type=FunctionParameterType.DICT, description="Filter conditions", required=False),
                FunctionParameter(name="limit", type=FunctionParameterType.INTEGER, description="Maximum number of records", required=False)
            ],
            returns="List of data records",
            examples=["retrieve_data('invoices', {'month': 'March'}, 100)"]
        ),
        retrieve_data
    )
    
    # Filter data
    registry.register_function(
        FunctionSchema(
            name="filter_data",
            description="Filter data based on specified conditions",
            category="data_operations",
            parameters=[
                FunctionParameter(name="data", type=FunctionParameterType.LIST, description="Data to filter"),
                FunctionParameter(name="conditions", type=FunctionParameterType.DICT, description="Filter conditions")
            ],
            returns="Filtered data list",
            examples=["filter_data(data, {'status': 'active'})"]
        ),
        filter_data
    )
    
    # Sort data
    registry.register_function(
        FunctionSchema(
            name="sort_data",
            description="Sort data by a specified field",
            category="data_operations",
            parameters=[
                FunctionParameter(name="data", type=FunctionParameterType.LIST, description="Data to sort"),
                FunctionParameter(name="sort_by", type=FunctionParameterType.STRING, description="Field to sort by"),
                FunctionParameter(name="ascending", type=FunctionParameterType.BOOLEAN, description="Sort order", required=False, default=True)
            ],
            returns="Sorted data list",
            examples=["sort_data(data, 'date', False)"]
        ),
        sort_data
    )
    
    # Group data
    registry.register_function(
        FunctionSchema(
            name="group_data",
            description="Group data by a specified field",
            category="data_operations",
            parameters=[
                FunctionParameter(name="data", type=FunctionParameterType.LIST, description="Data to group"),
                FunctionParameter(name="group_by", type=FunctionParameterType.STRING, description="Field to group by")
            ],
            returns="Dictionary of grouped data",
            examples=["group_data(data, 'category')"]
        ),
        group_data
    )
    
    # Aggregate data
    registry.register_function(
        FunctionSchema(
            name="aggregate_data",
            description="Aggregate data using specified operation",
            category="data_operations",
            parameters=[
                FunctionParameter(name="data", type=FunctionParameterType.LIST, description="Data to aggregate"),
                FunctionParameter(name="field", type=FunctionParameterType.STRING, description="Field to aggregate"),
                FunctionParameter(name="operation", type=FunctionParameterType.STRING, description="Aggregation operation", enum_values=["sum", "avg", "count", "min", "max"])
            ],
            returns="Aggregated value",
            examples=["aggregate_data(invoices, 'amount', 'sum')"]
        ),
        aggregate_data
    )
    
    # Transform data
    registry.register_function(
        FunctionSchema(
            name="transform_data",
            description="Transform data fields based on specified transformations",
            category="data_operations",
            parameters=[
                FunctionParameter(name="data", type=FunctionParameterType.LIST, description="Data to transform"),
                FunctionParameter(name="transformations", type=FunctionParameterType.DICT, description="Field transformations")
            ],
            returns="Transformed data list",
            examples=["transform_data(data, {'name': 'uppercase', 'amount': 'round'})"]
        ),
        transform_data
    )
    
    # Merge data
    registry.register_function(
        FunctionSchema(
            name="merge_data",
            description="Merge two datasets on a common key",
            category="data_operations",
            parameters=[
                FunctionParameter(name="data1", type=FunctionParameterType.LIST, description="First dataset"),
                FunctionParameter(name="data2", type=FunctionParameterType.LIST, description="Second dataset"),
                FunctionParameter(name="join_key", type=FunctionParameterType.STRING, description="Common key for joining")
            ],
            returns="Merged data list",
            examples=["merge_data(customers, orders, 'customer_id')"]
        ),
        merge_data
    )
    
    # Deduplicate data
    registry.register_function(
        FunctionSchema(
            name="deduplicate_data",
            description="Remove duplicate entries from data",
            category="data_operations",
            parameters=[
                FunctionParameter(name="data", type=FunctionParameterType.LIST, description="Data to deduplicate"),
                FunctionParameter(name="key", type=FunctionParameterType.STRING, description="Key field for deduplication", required=False)
            ],
            returns="Deduplicated data list",
            examples=["deduplicate_data(data, 'id')"]
        ),
        deduplicate_data
    )
